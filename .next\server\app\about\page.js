/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/about/page";
exports.ids = ["app/about/page"];
exports.modules = {

/***/ "(rsc)/./app/about/page.tsx":
/*!****************************!*\
  !*** ./app/about/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\about\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3fb1e4adba92\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmxhY2tcXERlc2t0b3BcXHNtYXJ0LW9mZi1wbGFuXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiM2ZiMWU0YWRiYTkyXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'Smart Off Plan - Dubai Property Investment',\n    description: 'Your trusted partner for Dubai property investments. Connecting international investors with premium off-plan opportunities.',\n    keywords: 'Dubai property, off-plan investment, real estate, property management, golden visa',\n    authors: [\n        {\n            name: 'Smart Off Plan'\n        }\n    ],\n    openGraph: {\n        title: 'Smart Off Plan - Dubai Property Investment',\n        description: 'Your trusted partner for Dubai property investments. Connecting international investors with premium off-plan opportunities.',\n        type: 'website',\n        locale: 'en_US'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/about/page.tsx */ \"(rsc)/./app/about/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'about',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/about/page\",\n        pathname: \"/about\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/about/page.tsx */ \"(rsc)/./app/about/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0JsYWNrJTVDJTVDRGVza3RvcCU1QyU1Q3NtYXJ0LW9mZi1wbGFuJTVDJTVDYXBwJTVDJTVDYWJvdXQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW9HIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxCbGFja1xcXFxEZXNrdG9wXFxcXHNtYXJ0LW9mZi1wbGFuXFxcXGFwcFxcXFxhYm91dFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/about/page.tsx":
/*!****************************!*\
  !*** ./app/about/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Navbar */ \"(ssr)/./app/components/Navbar.tsx\");\n/* harmony import */ var _components_AboutUsPage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AboutUsPage */ \"(ssr)/./app/components/AboutUsPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AboutPage() {\n    const handleLogoClick = ()=>{\n        // Navigate to home page\n        window.location.href = '/';\n    };\n    const handleBackToHome = ()=>{\n        window.location.href = '/';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-ivory\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__.Navbar, {\n                onLogoClick: handleLogoClick,\n                currentPage: \"about\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AboutUsPage__WEBPACK_IMPORTED_MODULE_2__.AboutUsPage, {\n                onBack: handleBackToHome\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-soft-brown text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"section-padding\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"mb-6 text-white\",\n                                                children: \"Smart Off Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-tan text-sm mb-6 leading-relaxed\",\n                                                children: \"Your trusted partner for Dubai property investments. Connecting international investors with premium off-plan opportunities.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mb-6 text-white\",\n                                                children: \"Quick Links\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Home\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 43,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 42,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/properties\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Projects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 48,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 47,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/developers\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Developers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 53,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 52,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/about\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"About Us\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 58,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 57,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/contact\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Contact\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 62,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mb-6 text-white\",\n                                                children: \"Services\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/services/company-formation\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Company Formation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/services/mortgages\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Mortgages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 80,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/services/golden-visa\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Golden Visa\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mb-6 text-white\",\n                                                children: \"Get In Touch\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-tan text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: \"\\uD83D\\uDCDE +971 4 123 4567\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 97,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: \"\\uD83D\\uDCE7 <EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 98,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: \"\\uD83D\\uDCCD Business Bay, Dubai, UAE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 99,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-white mb-3\",\n                                                                children: \"Working Hours\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 102,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-tan text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: \"Mon - Fri: 9:00 AM - 7:00 PM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                                        lineNumber: 104,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: \"Sat: 10:00 AM - 4:00 PM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                                        lineNumber: 105,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 103,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-12 h-px bg-gradient-to-r from-transparent via-gold to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-tan text-sm\",\n                                        children: \"\\xa9 2024 Smart Off Plan. All rights reserved.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-8 mt-4 md:mt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/privacy\",\n                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/terms\",\n                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/cookies\",\n                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                children: \"Cookie Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\about\\\\page.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvYWJvdXQvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRThDO0FBQ1U7QUFFekMsU0FBU0U7SUFDdEIsTUFBTUMsa0JBQWtCO1FBQ3RCLHdCQUF3QjtRQUN4QkMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7SUFDekI7SUFFQSxNQUFNQyxtQkFBbUI7UUFDdkJILE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO0lBQ3pCO0lBRUEscUJBQ0UsOERBQUNFO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDVCxzREFBTUE7Z0JBQ0xVLGFBQWFQO2dCQUNiUSxhQUFZOzs7Ozs7MEJBRWQsOERBQUNWLGdFQUFXQTtnQkFBQ1csUUFBUUw7Ozs7OzswQkFHckIsOERBQUNNO2dCQUFPSixXQUFVOzBCQUNoQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUViLDhEQUFDRDs7MERBQ0MsOERBQUNNO2dEQUFHTCxXQUFVOzBEQUFrQjs7Ozs7OzBEQUNoQyw4REFBQ007Z0RBQUVOLFdBQVU7MERBQXdDOzs7Ozs7Ozs7Ozs7a0RBT3ZELDhEQUFDRDs7MERBQ0MsOERBQUNRO2dEQUFHUCxXQUFVOzBEQUFrQjs7Ozs7OzBEQUNoQyw4REFBQ1E7Z0RBQUdSLFdBQVU7O2tFQUNaLDhEQUFDUztrRUFDQyw0RUFBQ0M7NERBQUViLE1BQUs7NERBQUlHLFdBQVU7c0VBQXFEOzs7Ozs7Ozs7OztrRUFJN0UsOERBQUNTO2tFQUNDLDRFQUFDQzs0REFBRWIsTUFBSzs0REFBY0csV0FBVTtzRUFBcUQ7Ozs7Ozs7Ozs7O2tFQUl2Riw4REFBQ1M7a0VBQ0MsNEVBQUNDOzREQUFFYixNQUFLOzREQUFjRyxXQUFVO3NFQUFxRDs7Ozs7Ozs7Ozs7a0VBSXZGLDhEQUFDUztrRUFDQyw0RUFBQ0M7NERBQUViLE1BQUs7NERBQVNHLFdBQVU7c0VBQXFEOzs7Ozs7Ozs7OztrRUFJbEYsOERBQUNTO2tFQUNDLDRFQUFDQzs0REFBRWIsTUFBSzs0REFBV0csV0FBVTtzRUFBcUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQVF4Riw4REFBQ0Q7OzBEQUNDLDhEQUFDUTtnREFBR1AsV0FBVTswREFBa0I7Ozs7OzswREFDaEMsOERBQUNRO2dEQUFHUixXQUFVOztrRUFDWiw4REFBQ1M7a0VBQ0MsNEVBQUNDOzREQUFFYixNQUFLOzREQUE4QkcsV0FBVTtzRUFBcUQ7Ozs7Ozs7Ozs7O2tFQUl2Ryw4REFBQ1M7a0VBQ0MsNEVBQUNDOzREQUFFYixNQUFLOzREQUFzQkcsV0FBVTtzRUFBcUQ7Ozs7Ozs7Ozs7O2tFQUkvRiw4REFBQ1M7a0VBQ0MsNEVBQUNDOzREQUFFYixNQUFLOzREQUF3QkcsV0FBVTtzRUFBcUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQVFyRyw4REFBQ0Q7OzBEQUNDLDhEQUFDUTtnREFBR1AsV0FBVTswREFBa0I7Ozs7OzswREFDaEMsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFBTzs7Ozs7OzBFQUN0Qiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQU87Ozs7OzswRUFDdEIsOERBQUNEO2dFQUFJQyxXQUFVOzBFQUFPOzs7Ozs7Ozs7Ozs7a0VBRXhCLDhEQUFDRDs7MEVBQ0MsOERBQUNZO2dFQUFHWCxXQUFVOzBFQUFrQjs7Ozs7OzBFQUNoQyw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDtrRkFBSTs7Ozs7O2tGQUNMLDhEQUFDQTtrRkFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVFmLDhEQUFDQTtnQ0FBSUMsV0FBVTs7Ozs7OzBDQUdmLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUFtQjs7Ozs7O2tEQUdsQyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDVTtnREFBRWIsTUFBSztnREFBV0csV0FBVTswREFBcUQ7Ozs7OzswREFHbEYsOERBQUNVO2dEQUFFYixNQUFLO2dEQUFTRyxXQUFVOzBEQUFxRDs7Ozs7OzBEQUdoRiw4REFBQ1U7Z0RBQUViLE1BQUs7Z0RBQVdHLFdBQVU7MERBQXFEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVWxHIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEJsYWNrXFxEZXNrdG9wXFxzbWFydC1vZmYtcGxhblxcYXBwXFxhYm91dFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IE5hdmJhciB9IGZyb20gXCIuLi9jb21wb25lbnRzL05hdmJhclwiO1xuaW1wb3J0IHsgQWJvdXRVc1BhZ2UgfSBmcm9tIFwiLi4vY29tcG9uZW50cy9BYm91dFVzUGFnZVwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBYm91dFBhZ2UoKSB7XG4gIGNvbnN0IGhhbmRsZUxvZ29DbGljayA9ICgpID0+IHtcbiAgICAvLyBOYXZpZ2F0ZSB0byBob21lIHBhZ2VcbiAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvJztcbiAgfTtcblxuICBjb25zdCBoYW5kbGVCYWNrVG9Ib21lID0gKCkgPT4ge1xuICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy8nO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctaXZvcnlcIj5cbiAgICAgIDxOYXZiYXIgXG4gICAgICAgIG9uTG9nb0NsaWNrPXtoYW5kbGVMb2dvQ2xpY2t9IFxuICAgICAgICBjdXJyZW50UGFnZT1cImFib3V0XCIgXG4gICAgICAvPlxuICAgICAgPEFib3V0VXNQYWdlIG9uQmFjaz17aGFuZGxlQmFja1RvSG9tZX0gLz5cbiAgICAgIFxuICAgICAgey8qIEZvb3RlciAqL31cbiAgICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctc29mdC1icm93biB0ZXh0LXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2VjdGlvbi1wYWRkaW5nXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtMTJcIj5cbiAgICAgICAgICAgICAgey8qIENvbXBhbnkgSW5mbyAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwibWItNiB0ZXh0LXdoaXRlXCI+U21hcnQgT2ZmIFBsYW48L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtdGFuIHRleHQtc20gbWItNiBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICAgIFlvdXIgdHJ1c3RlZCBwYXJ0bmVyIGZvciBEdWJhaSBwcm9wZXJ0eSBpbnZlc3RtZW50cy4gQ29ubmVjdGluZ1xuICAgICAgICAgICAgICAgICAgaW50ZXJuYXRpb25hbCBpbnZlc3RvcnMgd2l0aCBwcmVtaXVtIG9mZi1wbGFuIG9wcG9ydHVuaXRpZXMuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogUXVpY2sgTGlua3MgKi99XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cIm1iLTYgdGV4dC13aGl0ZVwiPlF1aWNrIExpbmtzPC9oND5cbiAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwidGV4dC10YW4gaG92ZXI6dGV4dC1nb2xkIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICBIb21lXG4gICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIvcHJvcGVydGllc1wiIGNsYXNzTmFtZT1cInRleHQtdGFuIGhvdmVyOnRleHQtZ29sZCB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgUHJvamVjdHNcbiAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIi9kZXZlbG9wZXJzXCIgY2xhc3NOYW1lPVwidGV4dC10YW4gaG92ZXI6dGV4dC1nb2xkIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICBEZXZlbG9wZXJzXG4gICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIvYWJvdXRcIiBjbGFzc05hbWU9XCJ0ZXh0LXRhbiBob3Zlcjp0ZXh0LWdvbGQgdHJhbnNpdGlvbi1jb2xvcnMgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgIEFib3V0IFVzXG4gICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIvY29udGFjdFwiIGNsYXNzTmFtZT1cInRleHQtdGFuIGhvdmVyOnRleHQtZ29sZCB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQ29udGFjdFxuICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBTZXJ2aWNlcyAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwibWItNiB0ZXh0LXdoaXRlXCI+U2VydmljZXM8L2g0PlxuICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIi9zZXJ2aWNlcy9jb21wYW55LWZvcm1hdGlvblwiIGNsYXNzTmFtZT1cInRleHQtdGFuIGhvdmVyOnRleHQtZ29sZCB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQ29tcGFueSBGb3JtYXRpb25cbiAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIi9zZXJ2aWNlcy9tb3J0Z2FnZXNcIiBjbGFzc05hbWU9XCJ0ZXh0LXRhbiBob3Zlcjp0ZXh0LWdvbGQgdHJhbnNpdGlvbi1jb2xvcnMgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgIE1vcnRnYWdlc1xuICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgICAgICA8YSBocmVmPVwiL3NlcnZpY2VzL2dvbGRlbi12aXNhXCIgY2xhc3NOYW1lPVwidGV4dC10YW4gaG92ZXI6dGV4dC1nb2xkIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICBHb2xkZW4gVmlzYVxuICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBDb250YWN0IEluZm8gKi99XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cIm1iLTYgdGV4dC13aGl0ZVwiPkdldCBJbiBUb3VjaDwvaDQ+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC10YW4gdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTNcIj7wn5OeICs5NzEgNCAxMjMgNDU2NzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTNcIj7wn5OnIGluZm9Ac21hcnRvZmZwbGFuLmFlPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItM1wiPvCfk40gQnVzaW5lc3MgQmF5LCBEdWJhaSwgVUFFPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIG1iLTNcIj5Xb3JraW5nIEhvdXJzPC9oNT5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXRhbiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5Nb24gLSBGcmk6IDk6MDAgQU0gLSA3OjAwIFBNPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5TYXQ6IDEwOjAwIEFNIC0gNDowMCBQTTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTHV4dXJ5IERpdmlkZXIgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTEyIGgtcHggYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS1nb2xkIHRvLXRyYW5zcGFyZW50XCI+PC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBCb3R0b20gRm9vdGVyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXRhbiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgwqkgMjAyNCBTbWFydCBPZmYgUGxhbi4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTggbXQtNCBtZDptdC0wXCI+XG4gICAgICAgICAgICAgICAgPGEgaHJlZj1cIi9wcml2YWN5XCIgY2xhc3NOYW1lPVwidGV4dC10YW4gaG92ZXI6dGV4dC1nb2xkIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIFByaXZhY3kgUG9saWN5XG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIvdGVybXNcIiBjbGFzc05hbWU9XCJ0ZXh0LXRhbiBob3Zlcjp0ZXh0LWdvbGQgdHJhbnNpdGlvbi1jb2xvcnMgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgVGVybXMgb2YgU2VydmljZVxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8YSBocmVmPVwiL2Nvb2tpZXNcIiBjbGFzc05hbWU9XCJ0ZXh0LXRhbiBob3Zlcjp0ZXh0LWdvbGQgdHJhbnNpdGlvbi1jb2xvcnMgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgQ29va2llIFBvbGljeVxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Zvb3Rlcj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJOYXZiYXIiLCJBYm91dFVzUGFnZSIsIkFib3V0UGFnZSIsImhhbmRsZUxvZ29DbGljayIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsImhhbmRsZUJhY2tUb0hvbWUiLCJkaXYiLCJjbGFzc05hbWUiLCJvbkxvZ29DbGljayIsImN1cnJlbnRQYWdlIiwib25CYWNrIiwiZm9vdGVyIiwiaDMiLCJwIiwiaDQiLCJ1bCIsImxpIiwiYSIsImg1Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/about/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/AboutUsPage.tsx":
/*!****************************************!*\
  !*** ./app/components/AboutUsPage.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AboutUsPage: () => (/* binding */ AboutUsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./app/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./app/components/ui/card.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/badge */ \"(ssr)/./app/components/ui/badge.tsx\");\n/* harmony import */ var _ui_accordion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/accordion */ \"(ssr)/./app/components/ui/accordion.tsx\");\n/* harmony import */ var _ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/breadcrumb */ \"(ssr)/./app/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/timer.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,BarChart3,Building2,Calendar,CheckCircle,CreditCard,DollarSign,FileText,Home,MapPin,MessageCircle,Settings,Shield,ShieldCheck,Star,Target,Timer,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n\n\n\n\n\n\n\n\nfunction AboutUsPage({ onBack }) {\n    const [activeTimelineStep, setActiveTimelineStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const advantages = [\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Lower Entry Prices\",\n            description: \"Off-plan properties are typically priced 20-30% below market value, offering significant savings compared to ready properties.\",\n            highlight: \"20-30% Savings\",\n            color: \"text-gold\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: \"Capital Appreciation\",\n            description: \"Properties often appreciate 15-25% from launch to completion, providing substantial returns on investment.\",\n            highlight: \"15-25% Growth\",\n            color: \"text-soft-brown\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: \"Flexible Payment Plans\",\n            description: \"Spread payments over construction period with minimal initial deposits, improving cash flow management.\",\n            highlight: \"Flexible Terms\",\n            color: \"text-gold\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"Latest Designs & Technology\",\n            description: \"Brand new properties with modern amenities, smart home features, and contemporary architectural designs.\",\n            highlight: \"Modern Living\",\n            color: \"text-soft-brown\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"Prime Location Access\",\n            description: \"Secure units in highly sought-after developments before they're completed and prices increase.\",\n            highlight: \"Prime Locations\",\n            color: \"text-gold\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Developer Incentives\",\n            description: \"Exclusive offers, payment plan benefits, and special terms available only during pre-launch phases.\",\n            highlight: \"Exclusive Deals\",\n            color: \"text-soft-brown\"\n        }\n    ];\n    const risks = [\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Construction Delays\",\n            description: \"Projects may face delays due to market conditions, permits, or unforeseen circumstances.\",\n            mitigation: \"Choose established developers with proven track records\",\n            iconColor: \"text-orange-500\",\n            bgColor: \"from-orange-100 to-orange-50\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"Market Fluctuations\",\n            description: \"Property values may change during the construction period due to market dynamics.\",\n            mitigation: \"Research market trends and choose prime locations\",\n            iconColor: \"text-red-500\",\n            bgColor: \"from-red-100 to-red-50\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            title: \"Developer Financial Issues\",\n            description: \"Developer financial difficulties could impact project completion.\",\n            mitigation: \"Verify developer credentials and financial stability\",\n            iconColor: \"text-purple-500\",\n            bgColor: \"from-purple-100 to-purple-50\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            title: \"Specification Changes\",\n            description: \"Final specifications may differ slightly from initial marketing materials.\",\n            mitigation: \"Review contracts carefully and document agreed specifications\",\n            iconColor: \"text-blue-500\",\n            bgColor: \"from-blue-100 to-blue-50\"\n        }\n    ];\n    const timelineSteps = [\n        {\n            phase: \"Research & Selection\",\n            duration: \"1-2 Weeks\",\n            description: \"Property research, location analysis, and developer verification\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            color: \"bg-gold\"\n        },\n        {\n            phase: \"Booking & Reservation\",\n            duration: \"1-3 Days\",\n            description: \"Unit reservation with booking fee and initial documentation\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            color: \"bg-soft-brown\"\n        },\n        {\n            phase: \"Contract Signing\",\n            duration: \"1-2 Weeks\",\n            description: \"Sales purchase agreement review and contract execution\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            color: \"bg-gold\"\n        },\n        {\n            phase: \"Payment Schedule\",\n            duration: \"24-36 Months\",\n            description: \"Installment payments linked to construction milestones\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"bg-soft-brown\"\n        },\n        {\n            phase: \"Construction Phase\",\n            duration: \"24-36 Months\",\n            description: \"Property construction with regular progress updates\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"bg-gold\"\n        },\n        {\n            phase: \"Pre-Handover\",\n            duration: \"3-6 Months\",\n            description: \"Final inspections, snagging, and completion preparations\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            color: \"bg-soft-brown\"\n        },\n        {\n            phase: \"Handover & Ownership\",\n            duration: \"1-2 Weeks\",\n            description: \"Key handover, title deed transfer, and property registration\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            color: \"bg-gold\"\n        }\n    ];\n    const faqs = [\n        {\n            question: \"What exactly is an off-plan property?\",\n            answer: \"An off-plan property is a real estate investment where you purchase a property before it's built or completed. You're essentially buying based on architectural plans, 3D renderings, and developer specifications. Payment is typically made in installments during the construction phase.\"\n        },\n        {\n            question: \"How much deposit do I need for off-plan property in Dubai?\",\n            answer: \"Deposits for off-plan properties in Dubai typically range from 5-20% of the total property value. The exact amount depends on the developer, project type, and payment plan. Many developers offer flexible payment schedules with low initial deposits.\"\n        },\n        {\n            question: \"Is off-plan investment safe in Dubai?\",\n            answer: \"Dubai has robust regulations protecting off-plan investors. The Dubai Land Department (DLD) regulates all developments, and funds are held in escrow accounts. Additionally, developers must obtain permits and approvals before selling units, providing investor protection.\"\n        },\n        {\n            question: \"Can foreigners buy off-plan properties in Dubai?\",\n            answer: \"Yes, foreigners can purchase off-plan properties in designated freehold areas of Dubai. This includes popular areas like Dubai Marina, Downtown Dubai, Palm Jumeirah, and many other prime locations. Foreign ownership is 100% freehold in these areas.\"\n        },\n        {\n            question: \"What happens if the developer faces financial difficulties?\",\n            answer: \"Dubai's regulatory framework includes several protections: escrow accounts for buyer funds, developer registration requirements, and project insurance. If issues arise, the DLD can facilitate solutions, including project transfers to other developers.\"\n        },\n        {\n            question: \"How long does off-plan construction typically take?\",\n            answer: \"Construction timelines vary by project size and complexity, typically ranging from 18 months to 4 years. Apartments and townhouses usually take 2-3 years, while larger developments with amenities may take 3-4 years. Developers provide estimated completion dates in contracts.\"\n        },\n        {\n            question: \"Can I get a mortgage for off-plan properties?\",\n            answer: \"Yes, many UAE banks offer off-plan mortgages. However, terms may differ from ready property mortgages. Typically, you'll need to pay the initial installments yourself, and the mortgage begins closer to completion. Down payments for off-plan mortgages are usually higher.\"\n        },\n        {\n            question: \"What are the additional costs when buying off-plan?\",\n            answer: \"Additional costs include Dubai Land Department registration fees (4% of property value), real estate agent commission (2%), mortgage processing fees (if applicable), and legal fees. These costs are typically paid upon completion or during the process.\"\n        }\n    ];\n    const gettingStartedSteps = [\n        {\n            step: \"1\",\n            title: \"Define Your Investment Goals\",\n            description: \"Determine your budget, investment timeline, and whether you're buying for capital appreciation, rental income, or personal use.\"\n        },\n        {\n            step: \"2\",\n            title: \"Research Developers & Projects\",\n            description: \"Investigate developer track records, project locations, amenities, and payment plans. Focus on established developers with successful completions.\"\n        },\n        {\n            step: \"3\",\n            title: \"Secure Financing (If Needed)\",\n            description: \"Explore mortgage options, get pre-approved if necessary, and understand the payment schedule alignment with your financial capacity.\"\n        },\n        {\n            step: \"4\",\n            title: \"Professional Consultation\",\n            description: \"Engage with real estate experts, legal advisors, and financial consultants to ensure informed decision-making.\"\n        },\n        {\n            step: \"5\",\n            title: \"Make Your Investment\",\n            description: \"Complete due diligence, review all documentation, and proceed with booking your chosen off-plan property.\"\n        }\n    ];\n    const heroStats = [\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            value: \"500+\",\n            label: \"Off-Plan Projects\",\n            color: \"text-gold\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            value: \"25%\",\n            label: \"Average ROI\",\n            color: \"text-soft-brown\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            value: \"10,000+\",\n            label: \"Satisfied Investors\",\n            color: \"text-gold\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            value: \"4.9\",\n            label: \"Client Rating\",\n            color: \"text-soft-brown\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-ivory to-beige\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-soft-brown/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.Breadcrumb, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                onClick: onBack,\n                                                className: \"flex items-center gap-2 text-soft-brown hover:text-gold transition-colors duration-300 cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {\n                                            className: \"text-warm-gray\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbPage, {\n                                                className: \"text-soft-brown font-medium\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: onBack,\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"text-soft-brown hover:text-gold hover:bg-beige/50 transition-all duration-300 rounded-xl px-3 py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Back to Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative section-padding overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-beige/50 via-ivory to-beige/30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-1/2 h-full bg-gradient-to-bl from-gold/5 via-transparent to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 right-20 w-32 h-32 bg-gradient-to-br from-gold/10 to-gold/5 rounded-3xl rotate-12 hidden lg:block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 right-40 w-20 h-20 bg-gradient-to-br from-soft-brown/10 to-soft-brown/5 rounded-2xl -rotate-12 hidden lg:block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-60 w-16 h-16 bg-gradient-to-br from-gold/15 to-gold/8 rounded-xl rotate-45 hidden lg:block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center px-4 py-2 bg-gradient-to-r from-gold/10 to-gold/5 rounded-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-5 h-5 text-gold mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-soft-brown text-sm font-medium\",\n                                                            children: \"Dubai Property Investment Guide\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-soft-brown leading-tight text-[48px]\",\n                                                    children: [\n                                                        \"What is \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gold\",\n                                                            children: \"Off-Plan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        \" Investment?\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-warm-gray text-xl leading-relaxed\",\n                                                    children: \"Discover the world of off-plan property investment in Dubai - from understanding the basics to maximizing your returns in one of the world's most dynamic real estate markets.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"bg-gold hover:bg-gold/90 text-soft-brown px-8 py-4 rounded-xl group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Browse Off-Plan Properties\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"border-soft-brown text-soft-brown hover:bg-soft-brown hover:text-white px-8 py-4 rounded-xl group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Speak with Expert\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-6 pt-8 border-t border-soft-brown/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-gold rounded-full mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-soft-brown text-2xl\",\n                                                                    children: \"2-4 Years\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-warm-gray text-sm\",\n                                                            children: \"Typical Construction Period\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-soft-brown rounded-full mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-soft-brown text-2xl\",\n                                                                    children: \"5-20%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-warm-gray text-sm\",\n                                                            children: \"Initial Deposit Range\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-6 mb-8\",\n                                            children: heroStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                    className: \"bg-white/70 backdrop-blur-sm rounded-2xl shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_12px_40px_-4px_rgba(139,115,85,0.15),0_6px_20px_-4px_rgba(139,115,85,0.1)] hover:-translate-y-2 transition-all duration-300 border-0 group\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                        className: \"p-6 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-12 h-12 ${stat.color === 'text-gold' ? 'bg-gold/10' : 'bg-soft-brown/10'} rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                                    className: `w-6 h-6 ${stat.color}`\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `text-2xl mb-2 ${stat.color}`,\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-warm-gray text-sm\",\n                                                                children: stat.label\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"bg-gradient-to-br from-gold via-gold/90 to-gold/80 text-white rounded-3xl shadow-[0_8px_32px_-4px_rgba(212,175,55,0.3),0_4px_16px_-4px_rgba(212,175,55,0.2)] border-0 overflow-hidden group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-8 h-8 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                className: \"bg-white/20 text-white border-white/30 backdrop-blur-sm\",\n                                                                children: \"Market Leader\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white mb-4 text-xl\",\n                                                        children: \"Dubai's #1 Off-Plan Platform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/90 text-sm leading-relaxed mb-6\",\n                                                        children: \"Trusted by thousands of investors worldwide for premium off-plan opportunities in Dubai's most sought-after developments.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mr-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white/90 text-sm\",\n                                                                        children: \"Dubai, UAE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white/90 text-sm\",\n                                                                        children: \"4.9/5 Rating\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 -right-4 w-8 h-8 bg-gold/20 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-4 -left-4 w-6 h-6 bg-soft-brown/20 rounded-full animate-pulse\",\n                                            style: {\n                                                animationDelay: '1s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-soft-brown mb-8 text-[36px] text-[40px]\",\n                                        children: \"Understanding Off-Plan Investment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-warm-gray text-lg leading-relaxed\",\n                                                children: \"Off-plan property investment involves purchasing real estate before construction is completed, often during the planning or early construction phases. This investment strategy has become increasingly popular in Dubai's dynamic property market.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-warm-gray text-lg leading-relaxed\",\n                                                children: \"Investors purchase based on architectural plans, 3D renderings, and showroom displays, with payments typically structured over the construction period. This approach offers unique advantages including lower entry costs and potential for significant capital appreciation.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-beige rounded-2xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl text-gold mb-2\",\n                                                                children: \"2-4 Years\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-soft-brown text-sm\",\n                                                                children: \"Typical Construction Period\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-beige rounded-2xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl text-gold mb-2\",\n                                                                children: \"5-20%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-soft-brown text-sm\",\n                                                                children: \"Initial Deposit Range\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-gold to-soft-brown rounded-3xl p-8 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white mb-6 text-2xl\",\n                                            children: \"Key Characteristics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white mt-1 mr-4 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white mb-1\",\n                                                                    children: \"Pre-Construction Purchase\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white/80 text-sm\",\n                                                                    children: \"Buy before the property is built\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white mt-1 mr-4 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white mb-1\",\n                                                                    children: \"Installment Payments\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white/80 text-sm\",\n                                                                    children: \"Pay in stages during construction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white mt-1 mr-4 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white mb-1\",\n                                                                    children: \"Below Market Pricing\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white/80 text-sm\",\n                                                                    children: \"Access to pre-launch discounts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white mt-1 mr-4 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white mb-1\",\n                                                                    children: \"New & Modern\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white/80 text-sm\",\n                                                                    children: \"Latest designs and technology\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-gradient-to-br from-beige to-ivory\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-soft-brown mb-8 text-[36px] text-[40px]\",\n                                    children: \"Advantages of Off-Plan Investment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-warm-gray text-lg max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"Off-plan investment offers compelling benefits that make it an attractive option for both seasoned investors and first-time buyers in Dubai's property market.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: advantages.map((advantage, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-white rounded-3xl shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_12px_40px_-4px_rgba(139,115,85,0.15),0_6px_20px_-4px_rgba(139,115,85,0.1)] hover:-translate-y-2 transition-all duration-300 border-0 overflow-hidden group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-8 h-full flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-14 h-14 bg-gradient-to-br from-gold to-gold/80 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(advantage.icon, {\n                                                            className: \"w-7 h-7 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"bg-gold/10 text-gold border-gold/20 text-xs px-3 py-1\",\n                                                        children: advantage.highlight\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-soft-brown mb-4 group-hover:text-gold transition-colors\",\n                                                children: advantage.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-warm-gray text-sm leading-relaxed flex-grow\",\n                                                children: advantage.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                    lineNumber: 471,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                lineNumber: 470,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-soft-brown mb-8 text-[36px] text-[40px]\",\n                                    children: \"Off-Plan Investment Journey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-warm-gray text-lg max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"Follow the complete journey from property selection to handover. Understanding each phase helps you plan and track your investment progress.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-8 left-0 right-0 h-1 bg-gradient-to-r from-gold via-soft-brown to-gold hidden lg:block\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-7 gap-6\",\n                                    children: timelineSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: `bg-white rounded-2xl shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] transition-all duration-300 border-0 cursor-pointer group ${activeTimelineStep === index ? 'ring-2 ring-gold' : ''}`,\n                                                onClick: ()=>setActiveTimelineStep(index),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-12 h-12 ${step.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 relative`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                                                    className: \"w-6 h-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                index < timelineSteps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-12 left-1/2 transform -translate-x-1/2 w-1 h-8 bg-gradient-to-b from-gold to-soft-brown lg:hidden\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-soft-brown mb-2 text-sm group-hover:text-gold transition-colors\",\n                                                            children: step.phase\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gold text-xs mb-3\",\n                                                            children: step.duration\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-warm-gray text-xs leading-relaxed\",\n                                                            children: step.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this),\n                        activeTimelineStep !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 bg-gradient-to-r from-gold/10 via-gold/5 to-gold/10 rounded-3xl p-8 border border-gold/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-soft-brown mb-4 text-2xl\",\n                                        children: timelineSteps[activeTimelineStep].phase\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-warm-gray mb-6 max-w-2xl mx-auto leading-relaxed\",\n                                        children: timelineSteps[activeTimelineStep].description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        className: \"bg-gold text-white px-4 py-2\",\n                                        children: [\n                                            \"Duration: \",\n                                            timelineSteps[activeTimelineStep].duration\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-gradient-to-br from-beige to-ivory\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-soft-brown mb-8 text-[36px] text-[40px]\",\n                                    children: \"Understanding the Risks\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-warm-gray text-lg max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"While off-plan investment offers great opportunities, it's important to understand potential risks and how to mitigate them through informed decision-making.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: risks.map((risk, index)=>{\n                                const IconComponent = risk.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-white rounded-3xl shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] hover:-translate-y-1 transition-all duration-300 border-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-12 h-12 bg-gradient-to-br ${risk.bgColor} rounded-xl flex items-center justify-center mr-4 flex-shrink-0 group-hover:scale-110 transition-transform duration-300`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: `w-6 h-6 ${risk.iconColor}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-soft-brown mb-2 group-hover:text-gold transition-colors\",\n                                                                children: risk.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-warm-gray text-sm leading-relaxed mb-4\",\n                                                                children: risk.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-100/50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-800 text-sm mb-1\",\n                                                                    children: \"Risk Mitigation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-700 text-xs leading-relaxed\",\n                                                                    children: risk.mitigation\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-3xl p-8 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-soft-brown mb-4\",\n                                        children: \"Professional Guidance is Key\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-warm-gray mb-6 max-w-2xl mx-auto\",\n                                        children: \"Working with experienced real estate professionals, legal advisors, and established developers significantly reduces risks and enhances your investment success.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-gold hover:bg-gold/90 text-soft-brown px-8\",\n                                        children: \"Get Expert Consultation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                lineNumber: 577,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-soft-brown mb-8 text-[36px] text-[40px]\",\n                                    children: \"How to Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-warm-gray text-lg max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"Ready to begin your off-plan investment journey? Follow these essential steps to make informed decisions and maximize your investment potential.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: gettingStartedSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"bg-white rounded-3xl shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] transition-all duration-300 border-0 overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-gradient-to-br from-gold to-gold/80 rounded-2xl flex items-center justify-center mr-6 flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-2xl\",\n                                                                children: step.step\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-soft-brown mb-4 text-xl\",\n                                                                    children: step.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-warm-gray leading-relaxed\",\n                                                                    children: step.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 17\n                                        }, this),\n                                        index < gettingStartedSteps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center my-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-8 bg-gradient-to-b from-gold to-soft-brown\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                    lineNumber: 639,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                lineNumber: 638,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-gradient-to-br from-beige to-ivory\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-soft-brown mb-8 text-[36px] text-[40px]\",\n                                    children: \"Frequently Asked Questions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-warm-gray text-lg max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"Get answers to the most common questions about off-plan property investment in Dubai.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_accordion__WEBPACK_IMPORTED_MODULE_5__.Accordion, {\n                                type: \"single\",\n                                collapsible: true,\n                                className: \"space-y-4\",\n                                children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_accordion__WEBPACK_IMPORTED_MODULE_5__.AccordionItem, {\n                                        value: `item-${index}`,\n                                        className: \"bg-white rounded-2xl border-0 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_accordion__WEBPACK_IMPORTED_MODULE_5__.AccordionTrigger, {\n                                                className: \"px-8 py-6 text-left hover:no-underline [&[data-state=open]]:text-gold\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-soft-brown hover:text-gold transition-colors pr-4\",\n                                                    children: faq.question\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_accordion__WEBPACK_IMPORTED_MODULE_5__.AccordionContent, {\n                                                className: \"px-8 pb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-warm-gray leading-relaxed\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                    lineNumber: 681,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                lineNumber: 680,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-gradient-to-r from-soft-brown to-gold text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-white mb-8 text-[36px] text-[40px]\",\n                                children: \"Ready to Start Your Off-Plan Investment Journey?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/90 text-lg mb-10 leading-relaxed\",\n                                children: \"Take the first step towards building your property portfolio in Dubai. Our experts are ready to guide you through every aspect of off-plan investment.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-white text-soft-brown hover:bg-white/90 px-8 py-4 text-lg rounded-xl text-[14px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Schedule Consultation\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        className: \"border-white text-[rgba(139,115,85,1)] hover:bg-white/10 px-8 py-4 text-lg rounded-xl text-[14px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_BarChart3_Building2_Calendar_CheckCircle_CreditCard_DollarSign_FileText_Home_MapPin_MessageCircle_Settings_Shield_ShieldCheck_Star_Target_Timer_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                                lineNumber: 725,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"View Available Projects\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                        lineNumber: 713,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                    lineNumber: 712,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n                lineNumber: 711,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\AboutUsPage.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/AboutUsPage.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./app/components/Navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./app/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _SmartOffPlanLogo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SmartOffPlanLogo */ \"(ssr)/./app/components/SmartOffPlanLogo.tsx\");\n\n\n\n\n\n\n\nfunction Navbar({ onNavigate, onLogoClick, currentPage }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Determine current page from pathname if not provided\n    const getCurrentPage = ()=>{\n        if (currentPage) return currentPage;\n        if (pathname === \"/\") return \"home\";\n        if (pathname === \"/properties\") return \"properties\";\n        if (pathname === \"/developers\") return \"developers\";\n        if (pathname === \"/about\") return \"about\";\n        if (pathname === \"/contact\") return \"contact\";\n        return \"home\";\n    };\n    const activePage = getCurrentPage();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navbar.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"Navbar.useEffect.handleScroll\"];\n            window.addEventListener(\"scroll\", handleScroll);\n            return ({\n                \"Navbar.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    const handleNavigation = (page)=>{\n        if (onNavigate) {\n            onNavigate(page);\n        }\n        setIsOpen(false); // Close mobile menu after navigation\n        setActiveDropdown(null); // Close dropdown after navigation\n    };\n    const handleDropdownEnter = (dropdownName)=>{\n        setActiveDropdown(dropdownName);\n    };\n    const handleDropdownLeave = ()=>{\n        setActiveDropdown(null);\n    };\n    const navItems = [\n        {\n            name: \"Home\",\n            page: \"home\",\n            href: \"/\"\n        },\n        {\n            name: \"Projects\",\n            page: \"properties\",\n            href: \"/properties\"\n        },\n        {\n            name: \"Developers\",\n            page: \"developers\",\n            href: \"/developers\"\n        },\n        {\n            name: \"Services\",\n            page: \"services\",\n            hasDropdown: true\n        },\n        {\n            name: \"About\",\n            page: \"about\",\n            href: \"/about\"\n        },\n        {\n            name: \"Contact\",\n            page: \"contact\",\n            href: \"/contact\"\n        }\n    ];\n    const serviceItems = [\n        {\n            name: \"Company Formation\",\n            page: \"company-formation\",\n            href: \"/services/company-formation\"\n        },\n        {\n            name: \"Mortgages\",\n            page: \"mortgages\",\n            href: \"/services/mortgages\"\n        },\n        {\n            name: \"Golden Visa\",\n            page: \"golden-visa\",\n            href: \"/services/golden-visa\"\n        }\n    ];\n    const joinItems = [\n        {\n            name: \"Join As A Partner\",\n            page: \"join-as-partner\",\n            href: \"/join/partner\"\n        },\n        {\n            name: \"Become a Franchisee\",\n            page: \"become-franchisee\",\n            href: \"/join/franchise\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? \"bg-white/95 backdrop-blur-md shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]\" : \"bg-white/90 backdrop-blur-sm\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-20\",\n                    children: [\n                        onLogoClick ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onLogoClick,\n                            className: \"hover:opacity-80 transition-opacity text-[16px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartOffPlanLogo__WEBPACK_IMPORTED_MODULE_5__.SmartOffPlanLogo, {\n                                className: \"h-12 w-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"hover:opacity-80 transition-opacity text-[16px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartOffPlanLogo__WEBPACK_IMPORTED_MODULE_5__.SmartOffPlanLogo, {\n                                className: \"h-12 w-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-1\",\n                            children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>handleDropdownEnter(item.name),\n                                        onMouseLeave: handleDropdownLeave,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"relative px-4 py-2 text-soft-brown hover:text-gold transition-all duration-300 group flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10 text-sm font-medium tracking-wide\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: `w-3 h-3 ml-1 transition-transform duration-300 ${activeDropdown === item.name ? \"rotate-180\" : \"group-hover:rotate-180\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `absolute inset-0 bg-beige/50 rounded-lg transition-transform duration-300 origin-center ${activeDropdown === item.name ? \"scale-100\" : \"scale-0 group-hover:scale-100\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `absolute bottom-0 left-1/2 h-0.5 bg-gold transition-all duration-300 ${activeDropdown === item.name ? \"w-6 -translate-x-1/2\" : \"w-0 group-hover:w-6 group-hover:left-1/2 group-hover:-translate-x-1/2\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `absolute top-full left-0 pt-1 w-64 z-50 transition-all duration-300 ${activeDropdown === item.name ? \"opacity-100 visible translate-y-0\" : \"opacity-0 invisible translate-y-2 pointer-events-none\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/95 backdrop-blur-md rounded-2xl shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] border border-gold/10 overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2\",\n                                                        children: serviceItems.map((service, serviceIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: service.href || \"/\",\n                                                                onClick: ()=>handleNavigation(service.page),\n                                                                className: \"w-full text-left px-4 py-3 text-sm text-soft-brown hover:text-gold hover:bg-beige/50 rounded-xl transition-all duration-300 flex items-center group/item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-3 text-gold group-hover/item:scale-110 transition-transform duration-200\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"group-hover/item:translate-x-1 transition-transform duration-200\",\n                                                                        children: service.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, service.name, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href || \"/\",\n                                        onClick: ()=>handleNavigation(item.page),\n                                        className: `relative px-4 py-2 transition-all duration-300 group ${activePage === item.page ? \"text-gold\" : \"text-soft-brown hover:text-gold\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 text-sm font-medium tracking-wide\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-beige/50 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300 origin-center\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `absolute bottom-0 left-1/2 h-0.5 bg-gold transition-all duration-300 ${activePage === item.page ? \"w-6 -translate-x-1/2\" : \"w-0 group-hover:w-6 group-hover:-translate-x-1/2\"}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                onMouseEnter: ()=>handleDropdownEnter(\"join-us\"),\n                                onMouseLeave: handleDropdownLeave,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        className: `bg-gold hover:bg-gold/90 text-soft-brown px-6 py-2 rounded-xl transition-all duration-300 hover:shadow-[0_4px_16px_-2px_rgba(212,175,55,0.3)] flex items-center ${activeDropdown === \"join-us\" ? \"scale-105 shadow-[0_4px_16px_-2px_rgba(212,175,55,0.3)]\" : \"hover:scale-105\"}`,\n                                        children: [\n                                            \"Join Us\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: `w-4 h-4 ml-2 transition-transform duration-300 ${activeDropdown === \"join-us\" ? \"rotate-180\" : \"\"}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `absolute top-full right-0 pt-1 w-56 z-50 transition-all duration-300 ${activeDropdown === \"join-us\" ? \"opacity-100 visible translate-y-0\" : \"opacity-0 invisible translate-y-2 pointer-events-none\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/95 backdrop-blur-md rounded-2xl shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] border border-gold/10 overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2\",\n                                                children: joinItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: item.href || \"/\",\n                                                        onClick: ()=>handleNavigation(item.page),\n                                                        className: \"w-full text-left px-4 py-3 text-sm text-soft-brown hover:text-gold hover:bg-beige/50 rounded-xl transition-all duration-300 flex items-center group/item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-3 text-gold group-hover/item:scale-110 transition-transform duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"group-hover/item:translate-x-1 transition-transform duration-200\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.name, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsOpen(!isOpen),\n                            className: \"lg:hidden w-10 h-10 flex items-center justify-center text-soft-brown hover:text-gold transition-colors duration-300\",\n                            children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 23\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 51\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden absolute top-full left-0 right-0 bg-white/95 backdrop-blur-md border-t border-gold/10 shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] z-40\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container py-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-4 py-3 text-soft-brown text-sm font-medium border-b border-gold/10\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 25\n                                                }, this),\n                                                serviceItems.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: service.href || \"/\",\n                                                        onClick: ()=>handleNavigation(service.page),\n                                                        className: \"w-full text-left px-8 py-3 text-warm-gray hover:text-gold hover:bg-beige/50 transition-all duration-300 rounded-xl flex items-center group/mobile\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-3 text-gold group-hover/mobile:scale-110 transition-transform duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"group-hover/mobile:translate-x-1 transition-transform duration-200\",\n                                                                children: service.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, service.name, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href || \"/\",\n                                            onClick: ()=>handleNavigation(item.page),\n                                            className: `w-full text-left px-4 py-3 transition-all duration-300 rounded-xl ${activePage === item.page ? \"text-gold bg-beige/50\" : \"text-soft-brown hover:text-gold hover:bg-beige/50\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium tracking-wide\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t border-gold/10 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-4 py-3 text-soft-brown text-sm font-medium\",\n                                            children: \"Join Us\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, this),\n                                        joinItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href || \"/\",\n                                                onClick: ()=>handleNavigation(item.page),\n                                                className: \"w-full text-left px-8 py-3 text-warm-gray hover:text-gold hover:bg-beige/50 transition-all duration-300 rounded-xl flex items-center group/mobile\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-3 text-gold group-hover/mobile:scale-110 transition-transform duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"group-hover/mobile:translate-x-1 transition-transform duration-200\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 21\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/SmartOffPlanLogo.tsx":
/*!*********************************************!*\
  !*** ./app/components/SmartOffPlanLogo.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartOffPlanLogo: () => (/* binding */ SmartOffPlanLogo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction SmartOffPlanLogo({ className = \"h-10 w-auto\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        viewBox: \"0 0 400 80\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"40\",\n                cy: \"40\",\n                r: \"30\",\n                stroke: \"#d4af37\",\n                strokeWidth: \"0\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\SmartOffPlanLogo.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                x: \"85\",\n                y: \"32\",\n                fontFamily: \"Playfair Display, serif\",\n                fontSize: \"20\",\n                fontWeight: \"600\",\n                fill: \"#8b7355\",\n                letterSpacing: \"1.5px\",\n                children: \"SMART OFF PLAN\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\SmartOffPlanLogo.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                x: \"85\",\n                y: \"52\",\n                fontFamily: \"Inter, sans-serif\",\n                fontSize: \"10\",\n                fontWeight: \"400\",\n                fill: \"#8a7968\",\n                letterSpacing: \"3px\",\n                children: \"INVEST SMART\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\SmartOffPlanLogo.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\SmartOffPlanLogo.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/SmartOffPlanLogo.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/accordion.tsx":
/*!*****************************************!*\
  !*** ./app/components/ui/accordion.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Accordion: () => (/* binding */ Accordion),\n/* harmony export */   AccordionContent: () => (/* binding */ AccordionContent),\n/* harmony export */   AccordionItem: () => (/* binding */ AccordionItem),\n/* harmony export */   AccordionTrigger: () => (/* binding */ AccordionTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-accordion */ \"(ssr)/./node_modules/@radix-ui/react-accordion/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./app/components/ui/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Accordion,AccordionItem,AccordionTrigger,AccordionContent auto */ \n\n\n\n\nfunction Accordion({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"accordion\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\accordion.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\nfunction AccordionItem({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        \"data-slot\": \"accordion-item\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b last:border-b-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\accordion.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction AccordionTrigger({ className, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__.Header, {\n        className: \"flex\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n            \"data-slot\": \"accordion-trigger\",\n            className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\", className),\n            ...props,\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\accordion.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\accordion.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\accordion.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\nfunction AccordionContent({ className, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        \"data-slot\": \"accordion-content\",\n        className: \"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"pt-0 pb-4\", className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\accordion.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\accordion.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/accordion.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./app/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./app/components/ui/utils.ts\");\n\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n            destructive: \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"span\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"badge\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/breadcrumb.tsx":
/*!******************************************!*\
  !*** ./app/components/ui/breadcrumb.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Breadcrumb: () => (/* binding */ Breadcrumb),\n/* harmony export */   BreadcrumbEllipsis: () => (/* binding */ BreadcrumbEllipsis),\n/* harmony export */   BreadcrumbItem: () => (/* binding */ BreadcrumbItem),\n/* harmony export */   BreadcrumbLink: () => (/* binding */ BreadcrumbLink),\n/* harmony export */   BreadcrumbList: () => (/* binding */ BreadcrumbList),\n/* harmony export */   BreadcrumbPage: () => (/* binding */ BreadcrumbPage),\n/* harmony export */   BreadcrumbSeparator: () => (/* binding */ BreadcrumbSeparator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MoreHorizontal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MoreHorizontal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./app/components/ui/utils.ts\");\n\n\n\n\n\nfunction Breadcrumb({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        \"aria-label\": \"breadcrumb\",\n        \"data-slot\": \"breadcrumb\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\nfunction BreadcrumbList({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n        \"data-slot\": \"breadcrumb-list\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\nfunction BreadcrumbItem({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        \"data-slot\": \"breadcrumb-item\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center gap-1.5\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\nfunction BreadcrumbLink({ asChild, className, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.Slot : \"a\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"breadcrumb-link\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"hover:text-foreground transition-colors\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\nfunction BreadcrumbPage({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        \"data-slot\": \"breadcrumb-page\",\n        role: \"link\",\n        \"aria-disabled\": \"true\",\n        \"aria-current\": \"page\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-foreground font-normal\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction BreadcrumbSeparator({ children, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        \"data-slot\": \"breadcrumb-separator\",\n        role: \"presentation\",\n        \"aria-hidden\": \"true\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"[&>svg]:size-3.5\", className),\n        ...props,\n        children: children ?? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\breadcrumb.tsx\",\n            lineNumber: 78,\n            columnNumber: 20\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\nfunction BreadcrumbEllipsis({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        \"data-slot\": \"breadcrumb-ellipsis\",\n        role: \"presentation\",\n        \"aria-hidden\": \"true\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex size-9 items-center justify-center\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"size-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\breadcrumb.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"More\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\breadcrumb.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/breadcrumb.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./app/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./app/components/ui/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9 rounded-md\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/card.tsx":
/*!************************************!*\
  !*** ./app/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./app/components/ui/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pt-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6 [&:last-child]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 pb-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy91aS9jYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQStCO0FBRUY7QUFFN0IsU0FBU0UsS0FBSyxFQUFFQyxTQUFTLEVBQUUsR0FBR0MsT0FBb0M7SUFDaEUscUJBQ0UsOERBQUNDO1FBQ0NDLGFBQVU7UUFDVkgsV0FBV0YsMENBQUVBLENBQ1gsc0VBQ0FFO1FBRUQsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFQSxTQUFTRyxXQUFXLEVBQUVKLFNBQVMsRUFBRSxHQUFHQyxPQUFvQztJQUN0RSxxQkFDRSw4REFBQ0M7UUFDQ0MsYUFBVTtRQUNWSCxXQUFXRiwwQ0FBRUEsQ0FDWCxtS0FDQUU7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHZjtBQUVBLFNBQVNJLFVBQVUsRUFBRUwsU0FBUyxFQUFFLEdBQUdDLE9BQW9DO0lBQ3JFLHFCQUNFLDhEQUFDSztRQUNDSCxhQUFVO1FBQ1ZILFdBQVdGLDBDQUFFQSxDQUFDLGdCQUFnQkU7UUFDN0IsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFQSxTQUFTTSxnQkFBZ0IsRUFBRVAsU0FBUyxFQUFFLEdBQUdDLE9BQW9DO0lBQzNFLHFCQUNFLDhEQUFDTztRQUNDTCxhQUFVO1FBQ1ZILFdBQVdGLDBDQUFFQSxDQUFDLHlCQUF5QkU7UUFDdEMsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFQSxTQUFTUSxXQUFXLEVBQUVULFNBQVMsRUFBRSxHQUFHQyxPQUFvQztJQUN0RSxxQkFDRSw4REFBQ0M7UUFDQ0MsYUFBVTtRQUNWSCxXQUFXRiwwQ0FBRUEsQ0FDWCxrRUFDQUU7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHZjtBQUVBLFNBQVNTLFlBQVksRUFBRVYsU0FBUyxFQUFFLEdBQUdDLE9BQW9DO0lBQ3ZFLHFCQUNFLDhEQUFDQztRQUNDQyxhQUFVO1FBQ1ZILFdBQVdGLDBDQUFFQSxDQUFDLDRCQUE0QkU7UUFDekMsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFQSxTQUFTVSxXQUFXLEVBQUVYLFNBQVMsRUFBRSxHQUFHQyxPQUFvQztJQUN0RSxxQkFDRSw4REFBQ0M7UUFDQ0MsYUFBVTtRQUNWSCxXQUFXRiwwQ0FBRUEsQ0FBQyxnREFBZ0RFO1FBQzdELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBVUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmxhY2tcXERlc2t0b3BcXHNtYXJ0LW9mZi1wbGFuXFxhcHBcXGNvbXBvbmVudHNcXHVpXFxjYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiLi91dGlsc1wiO1xuXG5mdW5jdGlvbiBDYXJkKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGRhdGEtc2xvdD1cImNhcmRcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJiZy1jYXJkIHRleHQtY2FyZC1mb3JlZ3JvdW5kIGZsZXggZmxleC1jb2wgZ2FwLTYgcm91bmRlZC14bCBib3JkZXJcIixcbiAgICAgICAgY2xhc3NOYW1lLFxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApO1xufVxuXG5mdW5jdGlvbiBDYXJkSGVhZGVyKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGRhdGEtc2xvdD1cImNhcmQtaGVhZGVyXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiQGNvbnRhaW5lci9jYXJkLWhlYWRlciBncmlkIGF1dG8tcm93cy1taW4gZ3JpZC1yb3dzLVthdXRvX2F1dG9dIGl0ZW1zLXN0YXJ0IGdhcC0xLjUgcHgtNiBwdC02IGhhcy1kYXRhLVtzbG90PWNhcmQtYWN0aW9uXTpncmlkLWNvbHMtWzFmcl9hdXRvXSBbLmJvcmRlci1iXTpwYi02XCIsXG4gICAgICAgIGNsYXNzTmFtZSxcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKTtcbn1cblxuZnVuY3Rpb24gQ2FyZFRpdGxlKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxoNFxuICAgICAgZGF0YS1zbG90PVwiY2FyZC10aXRsZVwiXG4gICAgICBjbGFzc05hbWU9e2NuKFwibGVhZGluZy1ub25lXCIsIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKTtcbn1cblxuZnVuY3Rpb24gQ2FyZERlc2NyaXB0aW9uKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxwXG4gICAgICBkYXRhLXNsb3Q9XCJjYXJkLWRlc2NyaXB0aW9uXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIiwgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApO1xufVxuXG5mdW5jdGlvbiBDYXJkQWN0aW9uKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGRhdGEtc2xvdD1cImNhcmQtYWN0aW9uXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiY29sLXN0YXJ0LTIgcm93LXNwYW4tMiByb3ctc3RhcnQtMSBzZWxmLXN0YXJ0IGp1c3RpZnktc2VsZi1lbmRcIixcbiAgICAgICAgY2xhc3NOYW1lLFxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApO1xufVxuXG5mdW5jdGlvbiBDYXJkQ29udGVudCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfTogUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJkaXZcIj4pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICBkYXRhLXNsb3Q9XCJjYXJkLWNvbnRlbnRcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcInB4LTYgWyY6bGFzdC1jaGlsZF06cGItNlwiLCBjbGFzc05hbWUpfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gICk7XG59XG5cbmZ1bmN0aW9uIENhcmRGb290ZXIoeyBjbGFzc05hbWUsIC4uLnByb3BzIH06IFJlYWN0LkNvbXBvbmVudFByb3BzPFwiZGl2XCI+KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgZGF0YS1zbG90PVwiY2FyZC1mb290ZXJcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcImZsZXggaXRlbXMtY2VudGVyIHB4LTYgcGItNiBbLmJvcmRlci10XTpwdC02XCIsIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKTtcbn1cblxuZXhwb3J0IHtcbiAgQ2FyZCxcbiAgQ2FyZEhlYWRlcixcbiAgQ2FyZEZvb3RlcixcbiAgQ2FyZFRpdGxlLFxuICBDYXJkQWN0aW9uLFxuICBDYXJkRGVzY3JpcHRpb24sXG4gIENhcmRDb250ZW50LFxufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiQ2FyZCIsImNsYXNzTmFtZSIsInByb3BzIiwiZGl2IiwiZGF0YS1zbG90IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsImg0IiwiQ2FyZERlc2NyaXB0aW9uIiwicCIsIkNhcmRBY3Rpb24iLCJDYXJkQ29udGVudCIsIkNhcmRGb290ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/utils.ts":
/*!************************************!*\
  !*** ./app/components/ui/utils.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy91aS91dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFDSjtBQUVsQyxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmxhY2tcXERlc2t0b3BcXHNtYXJ0LW9mZi1wbGFuXFxhcHBcXGNvbXBvbmVudHNcXHVpXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiO1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiO1xuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/about/page.tsx */ \"(ssr)/./app/about/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0JsYWNrJTVDJTVDRGVza3RvcCU1QyU1Q3NtYXJ0LW9mZi1wbGFuJTVDJTVDYXBwJTVDJTVDYWJvdXQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW9HIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxCbGFja1xcXFxEZXNrdG9wXFxcXHNtYXJ0LW9mZi1wbGFuXFxcXGFwcFxcXFxhYm91dFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0JsYWNrJTVDJTVDRGVza3RvcCU1QyU1Q3NtYXJ0LW9mZi1wbGFuJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQmxhY2slNUMlNUNEZXNrdG9wJTVDJTVDc21hcnQtb2ZmLXBsYW4lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNCbGFjayU1QyU1Q0Rlc2t0b3AlNUMlNUNzbWFydC1vZmYtcGxhbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0JsYWNrJTVDJTVDRGVza3RvcCU1QyU1Q3NtYXJ0LW9mZi1wbGFuJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDaHR0cC1hY2Nlc3MtZmFsbGJhY2slNUMlNUNlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNCbGFjayU1QyU1Q0Rlc2t0b3AlNUMlNUNzbWFydC1vZmYtcGxhbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQmxhY2slNUMlNUNEZXNrdG9wJTVDJTVDc21hcnQtb2ZmLXBsYW4lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0JsYWNrJTVDJTVDRGVza3RvcCU1QyU1Q3NtYXJ0LW9mZi1wbGFuJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbWV0YWRhdGElNUMlNUNtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNCbGFjayU1QyU1Q0Rlc2t0b3AlNUMlNUNzbWFydC1vZmYtcGxhbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUE0STtBQUM1STtBQUNBLDBPQUErSTtBQUMvSTtBQUNBLDBPQUErSTtBQUMvSTtBQUNBLG9SQUFxSztBQUNySztBQUNBLHdPQUE4STtBQUM5STtBQUNBLDRQQUF5SjtBQUN6SjtBQUNBLGtRQUE0SjtBQUM1SjtBQUNBLHNRQUE2SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQmxhY2tcXFxcRGVza3RvcFxcXFxzbWFydC1vZmYtcGxhblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxCbGFja1xcXFxEZXNrdG9wXFxcXHNtYXJ0LW9mZi1wbGFuXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEJsYWNrXFxcXERlc2t0b3BcXFxcc21hcnQtb2ZmLXBsYW5cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQmxhY2tcXFxcRGVza3RvcFxcXFxzbWFydC1vZmYtcGxhblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxCbGFja1xcXFxEZXNrdG9wXFxcXHNtYXJ0LW9mZi1wbGFuXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQmxhY2tcXFxcRGVza3RvcFxcXFxzbWFydC1vZmYtcGxhblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXGFzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxCbGFja1xcXFxEZXNrdG9wXFxcXHNtYXJ0LW9mZi1wbGFuXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEJsYWNrXFxcXERlc2t0b3BcXFxcc21hcnQtb2ZmLXBsYW5cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();