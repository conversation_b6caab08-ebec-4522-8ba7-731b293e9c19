'use client'

import { Navbar } from "../../components/Navbar";
import { GoldenVisaPage } from "../../components/GoldenVisaPage";

export default function GoldenVisaServicePage() {
  const handleLogoClick = () => {
    // Navigate to home page
    window.location.href = '/';
  };

  const handleBackToHome = () => {
    window.location.href = '/';
  };

  return (
    <div className="min-h-screen bg-ivory">
      <Navbar 
        onLogoClick={handleLogoClick} 
        currentPage="golden-visa" 
      />
      <GoldenVisaPage onBack={handleBackToHome} />
      
      {/* Footer */}
      <footer className="bg-soft-brown text-white">
        <div className="section-padding">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
              {/* Company Info */}
              <div>
                <h3 className="mb-6 text-white">Smart Off Plan</h3>
                <p className="text-tan text-sm mb-6 leading-relaxed">
                  Your trusted partner for Dubai property investments. Connecting
                  international investors with premium off-plan opportunities.
                </p>
              </div>

              {/* Quick Links */}
              <div>
                <h4 className="mb-6 text-white">Quick Links</h4>
                <ul className="space-y-3">
                  <li>
                    <a href="/" className="text-tan hover:text-gold transition-colors text-sm">
                      Home
                    </a>
                  </li>
                  <li>
                    <a href="/properties" className="text-tan hover:text-gold transition-colors text-sm">
                      Projects
                    </a>
                  </li>
                  <li>
                    <a href="/developers" className="text-tan hover:text-gold transition-colors text-sm">
                      Developers
                    </a>
                  </li>
                  <li>
                    <a href="/about" className="text-tan hover:text-gold transition-colors text-sm">
                      About Us
                    </a>
                  </li>
                  <li>
                    <a href="/contact" className="text-tan hover:text-gold transition-colors text-sm">
                      Contact
                    </a>
                  </li>
                </ul>
              </div>

              {/* Services */}
              <div>
                <h4 className="mb-6 text-white">Services</h4>
                <ul className="space-y-3">
                  <li>
                    <a href="/services/company-formation" className="text-tan hover:text-gold transition-colors text-sm">
                      Company Formation
                    </a>
                  </li>
                  <li>
                    <a href="/services/mortgages" className="text-tan hover:text-gold transition-colors text-sm">
                      Mortgages
                    </a>
                  </li>
                  <li>
                    <a href="/services/golden-visa" className="text-tan hover:text-gold transition-colors text-sm">
                      Golden Visa
                    </a>
                  </li>
                </ul>
              </div>

              {/* Contact Info */}
              <div>
                <h4 className="mb-6 text-white">Get In Touch</h4>
                <div className="space-y-4">
                  <div className="text-tan text-sm">
                    <div className="mb-3">📞 +971 4 123 4567</div>
                    <div className="mb-3">📧 <EMAIL></div>
                    <div className="mb-3">📍 Business Bay, Dubai, UAE</div>
                  </div>
                  <div>
                    <h5 className="text-white mb-3">Working Hours</h5>
                    <div className="text-tan text-sm">
                      <div>Mon - Fri: 9:00 AM - 7:00 PM</div>
                      <div>Sat: 10:00 AM - 4:00 PM</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Luxury Divider */}
            <div className="my-12 h-px bg-gradient-to-r from-transparent via-gold to-transparent"></div>

            {/* Bottom Footer */}
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-tan text-sm">
                © 2024 Smart Off Plan. All rights reserved.
              </div>
              <div className="flex space-x-8 mt-4 md:mt-0">
                <a href="/privacy" className="text-tan hover:text-gold transition-colors text-sm">
                  Privacy Policy
                </a>
                <a href="/terms" className="text-tan hover:text-gold transition-colors text-sm">
                  Terms of Service
                </a>
                <a href="/cookies" className="text-tan hover:text-gold transition-colors text-sm">
                  Cookie Policy
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
