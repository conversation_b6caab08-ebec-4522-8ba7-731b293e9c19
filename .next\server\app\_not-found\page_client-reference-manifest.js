globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/properties/page.tsx":{"*":{"id":"(ssr)/./app/properties/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/developers/page.tsx":{"*":{"id":"(ssr)/./app/developers/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/about/page.tsx":{"*":{"id":"(ssr)/./app/about/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(ssr)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/services/company-formation/page.tsx":{"*":{"id":"(ssr)/./app/services/company-formation/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/services/mortgages/page.tsx":{"*":{"id":"(ssr)/./app/services/mortgages/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/services/golden-visa/page.tsx":{"*":{"id":"(ssr)/./app/services/golden-visa/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/join/partner/page.tsx":{"*":{"id":"(ssr)/./app/join/partner/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/join/franchise/page.tsx":{"*":{"id":"(ssr)/./app/join/franchise/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\properties\\page.tsx":{"id":"(app-pages-browser)/./app/properties/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\developers\\page.tsx":{"id":"(app-pages-browser)/./app/developers/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\about\\page.tsx":{"id":"(app-pages-browser)/./app/about/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\contact\\page.tsx":{"id":"(app-pages-browser)/./app/contact/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\services\\company-formation\\page.tsx":{"id":"(app-pages-browser)/./app/services/company-formation/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\services\\mortgages\\page.tsx":{"id":"(app-pages-browser)/./app/services/mortgages/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\services\\golden-visa\\page.tsx":{"id":"(app-pages-browser)/./app/services/golden-visa/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\join\\partner\\page.tsx":{"id":"(app-pages-browser)/./app/join/partner/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\join\\franchise\\page.tsx":{"id":"(app-pages-browser)/./app/join/franchise/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\":[],"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\_not-found\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/properties/page.tsx":{"*":{"id":"(rsc)/./app/properties/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/developers/page.tsx":{"*":{"id":"(rsc)/./app/developers/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/about/page.tsx":{"*":{"id":"(rsc)/./app/about/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(rsc)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/services/company-formation/page.tsx":{"*":{"id":"(rsc)/./app/services/company-formation/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/services/mortgages/page.tsx":{"*":{"id":"(rsc)/./app/services/mortgages/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/services/golden-visa/page.tsx":{"*":{"id":"(rsc)/./app/services/golden-visa/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/join/partner/page.tsx":{"*":{"id":"(rsc)/./app/join/partner/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/join/franchise/page.tsx":{"*":{"id":"(rsc)/./app/join/franchise/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}