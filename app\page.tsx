"use client";

import { useState, useEffect } from "react";
import { SplashScreen } from "./components/SplashScreen";
import { Navbar } from "./components/Navbar";
import { HeroSection } from "./components/HeroSection";
import { FeaturedProjects } from "./components/FeaturedProjects";
import { PropertyFilters } from "./components/PropertyFilters";
import { DevelopersListing } from "./components/DevelopersListing";
import { PropertyListings } from "./components/PropertyListings";
import { MarketInfo } from "./components/MarketInfo";
import { AboutCompany } from "./components/AboutCompany";
import { Testimonials } from "./components/Testimonials";
import { ContactUs } from "./components/ContactUs";

import axios from "axios";

export default function HomePage() {
  // Enhanced splash screen state management for smooth transitions
  const [showSplash, setShowSplash] = useState(true);
  const [splashExiting, setSplashExiting] = useState(false);
  const [isMainContentReady, setIsMainContentReady] = useState(false);
  const [isLogoClickSplash, setIsLogoClickSplash] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);

  // Session management for splash screen
  useEffect(() => {
    // Check if user has already seen splash screen in this session
    const hasSeenSplash = sessionStorage.getItem("smart-off-plan-splash-seen");

    if (hasSeenSplash) {
      // Skip splash screen for this session with immediate content display
      setShowSplash(false);
      setSplashExiting(false);
      setIsMainContentReady(true);
      setContentVisible(true);
    } else {
      // Pre-render main content but keep it hidden for smooth transition
      setTimeout(() => {
        setIsMainContentReady(true);
      }, 2000); // Pre-render before splash completes
    }
  }, []);

  // Enhanced splash screen completion with perfect timing
  const handleSplashComplete = () => {
    // Mark splash as seen for this session (unless it's a logo click splash)
    if (!isLogoClickSplash) {
      sessionStorage.setItem("smart-off-plan-splash-seen", "true");
    }

    // Start smooth exit sequence
    setSplashExiting(true);

    // If it was a logo click splash, reset navigation states
    if (isLogoClickSplash) {
      setIsLogoClickSplash(false);
    }

    // Coordinate splash exit with content entrance for seamless transition
    setTimeout(() => {
      // Start content entrance while splash is still visible but fading
      setContentVisible(true);

      // Remove splash after content starts animating in
      setTimeout(() => {
        setShowSplash(false);
        setSplashExiting(false);

        // Smooth scroll to top for logo click splashes
        if (isLogoClickSplash) {
          window.scrollTo({ top: 0, behavior: "smooth" });
        }
      }, 400); // Allow overlap for smooth transition
    }, 600); // Start content transition before splash fully exits
  };

  // Enhanced logo click handler with proper state management
  const handleLogoClick = () => {
    // Set flag to indicate this is a logo click splash
    setIsLogoClickSplash(true);

    // Reset all transition states for fresh animation
    setContentVisible(false);
    setSplashExiting(false);

    // Pre-render content for smooth transition
    setIsMainContentReady(true);

    // Show splash screen again
    setShowSplash(true);

    // Don't update session storage for logo clicks - cinematic experience every time
  };

  // Main home page content
  const renderHomeContent = () => (
    <div className="min-h-screen bg-ivory">
      <Navbar onLogoClick={handleLogoClick} currentPage="home" />
      <HeroSection />
      <FeaturedProjects />
      <PropertyFilters />
      <DevelopersListing />
      <PropertyListings />
      <MarketInfo />
      <AboutCompany />
      <Testimonials />
      <ContactUs />

      {/* Footer */}
      <footer className="bg-soft-brown text-white">
        <div className="section-padding">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
              {/* Company Info */}
              <div>
                <h3 className="mb-6 text-white">Smart Off Plan</h3>
                <p className="text-tan text-sm mb-6 leading-relaxed">
                  Your trusted partner for Dubai property investments.
                  Connecting international investors with premium off-plan
                  opportunities.
                </p>
                <div className="flex space-x-4">
                  <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer">
                    <span className="text-sm">f</span>
                  </div>
                  <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer">
                    <span className="text-sm">t</span>
                  </div>
                  <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer">
                    <span className="text-sm">in</span>
                  </div>
                  <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer">
                    <span className="text-sm">ig</span>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div>
                <h4 className="mb-6 text-white">Quick Links</h4>
                <ul className="space-y-3">
                  <li>
                    <a
                      href="/"
                      className="text-tan hover:text-gold transition-colors text-sm"
                    >
                      Home
                    </a>
                  </li>
                  <li>
                    <a
                      href="/properties"
                      className="text-tan hover:text-gold transition-colors text-sm"
                    >
                      Projects
                    </a>
                  </li>
                  <li>
                    <a
                      href="/developers"
                      className="text-tan hover:text-gold transition-colors text-sm"
                    >
                      Developers
                    </a>
                  </li>
                  <li>
                    <a
                      href="/about"
                      className="text-tan hover:text-gold transition-colors text-sm"
                    >
                      About Us
                    </a>
                  </li>
                  <li>
                    <a
                      href="/contact"
                      className="text-tan hover:text-gold transition-colors text-sm"
                    >
                      Contact
                    </a>
                  </li>
                </ul>
              </div>

              {/* Services */}
              <div>
                <h4 className="mb-6 text-white">Services</h4>
                <ul className="space-y-3">
                  <li>
                    <a
                      href="/services/company-formation"
                      className="text-tan hover:text-gold transition-colors text-sm"
                    >
                      Company Formation
                    </a>
                  </li>
                  <li>
                    <a
                      href="/services/mortgages"
                      className="text-tan hover:text-gold transition-colors text-sm"
                    >
                      Mortgages
                    </a>
                  </li>
                  <li>
                    <a
                      href="/services/golden-visa"
                      className="text-tan hover:text-gold transition-colors text-sm"
                    >
                      Golden Visa
                    </a>
                  </li>
                </ul>
              </div>

              {/* Contact Info */}
              <div>
                <h4 className="mb-6 text-white">Get In Touch</h4>
                <div className="space-y-4">
                  <div className="text-tan text-sm">
                    <div className="mb-3">📞 +971 4 123 4567</div>
                    <div className="mb-3">📧 <EMAIL></div>
                    <div className="mb-3">📍 Business Bay, Dubai, UAE</div>
                  </div>
                  <div>
                    <h5 className="text-white mb-3">Working Hours</h5>
                    <div className="text-tan text-sm">
                      <div>Mon - Fri: 9:00 AM - 7:00 PM</div>
                      <div>Sat: 10:00 AM - 4:00 PM</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Luxury Divider */}
            <div className="my-12 h-px bg-gradient-to-r from-transparent via-gold to-transparent"></div>

            {/* Bottom Footer */}
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-tan text-sm">
                © 2024 Smart Off Plan. All rights reserved.
              </div>
              <div className="flex space-x-8 mt-4 md:mt-0">
                <a
                  href="/privacy"
                  className="text-tan hover:text-gold transition-colors text-sm"
                >
                  Privacy Policy
                </a>
                <a
                  href="/terms"
                  className="text-tan hover:text-gold transition-colors text-sm"
                >
                  Terms of Service
                </a>
                <a
                  href="/cookies"
                  className="text-tan hover:text-gold transition-colors text-sm"
                >
                  Cookie Policy
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );

  async function fetchAreas() {
    // Check if API configuration is available
    if (
      !process.env.BASE_URL ||
      !process.env.API_KEY
    ) {
      console.error("❌ Cannot fetch areas: API configuration missing");
      return null;
    }

    try {
      console.log("🔄 Fetching areas from API...");
      const response = await axios.get(
        `${process.env.BASE_URL}/api/areas`,
        {
          headers: {
            "X-API-Key": process.env.API_KEY,
          },
        }
      );
      console.log("✅ Areas fetched successfully:", response.data);
      return response.data;
    } catch (error: any) {
      console.error("❌ Error fetching areas:");
      console.error("Status:", error.response?.status);
      console.error("Data:", error.response?.data);
      console.error("Message:", error.message);

      // Return null instead of throwing to prevent app crash
      return null;
    }
  }

  // Test API connection on component mount
  useEffect(() => {
    const testAPI = async () => {
      console.log("🧪 Testing API connection...");
      const result = await fetchAreas();
      if (result) {
        console.log("✅ API test successful!");
      } else {
        console.log("❌ API test failed!");
      }
    };

    testAPI();
  }, [process.env.BASE_URL, process.env.API_KEY]);

  return (
    <div className="app-container">
      {/* Splash Screen Overlay */}
      {showSplash && (
        <div
          className={`splash-overlay ${
            splashExiting ? "splash-overlay-exiting" : ""
          }`}
        >
          <SplashScreen onComplete={handleSplashComplete} />
        </div>
      )}

      {/* Main Content */}
      {isMainContentReady && (
        <div
          className={`app-content ${
            !contentVisible
              ? "app-content-hidden"
              : contentVisible && !showSplash
              ? "app-content-visible"
              : "app-content-entering"
          }`}
        >
          {renderHomeContent()}
        </div>
      )}
    </div>
  );
}
