import { useState } from 'react';
import { But<PERSON> } from './ui/button';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Download, MapPin, Grid3X3, List, Bath, Bed, Square, Heart, TrendingUp } from 'lucide-react';
import { ImageWithFallback } from './figma/ImageWithFallback';

const properties = [
  {
    id: 1,
    name: "Luxury 2BR Apartment",
    location: "Dubai Marina",
    priceRange: "AED 2,850,000",
    status: "Off-plan",
    image: "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80",
    bedrooms: 2,
    bathrooms: 3,
    size: "1,245 sq ft",
    tags: ["Sea View", "Balcony", "Parking"],
    developer: "Emaar Properties",
    completion: "Q4 2025",
    roi: "8.5%"
  },
  {
    id: 2,
    name: "Premium Studio",
    location: "Downtown Dubai",
    priceRange: "AED 1,200,000",
    status: "Ready",
    image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80",
    bedrooms: 0,
    bathrooms: 1,
    size: "650 sq ft",
    tags: ["Furnished", "High Floor"],
    developer: "DAMAC Properties",
    completion: "Completed",
    roi: "7.2%"
  },
  {
    id: 3,
    name: "Spacious 3BR Villa",
    location: "Arabian Ranches",
    priceRange: "AED 4,500,000",
    status: "Under Construction",
    image: "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80",
    bedrooms: 3,
    bathrooms: 4,
    size: "2,800 sq ft",
    tags: ["Garden", "Pool", "Garage"],
    developer: "Dubai Properties",
    completion: "Q2 2026",
    roi: "9.1%"
  },
  {
    id: 4,
    name: "Modern 1BR Apartment",
    location: "Business Bay",
    priceRange: "AED 1,650,000",
    status: "Ready",
    image: "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80",
    bedrooms: 1,
    bathrooms: 2,
    size: "890 sq ft",
    tags: ["Canal View", "Gym Access"],
    developer: "Sobha Realty",
    completion: "Completed",
    roi: "6.8%"
  },
  {
    id: 5,
    name: "Penthouse Suite",
    location: "Palm Jumeirah",
    priceRange: "AED 15,000,000",
    status: "Off-plan",
    image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80",
    bedrooms: 4,
    bathrooms: 5,
    size: "4,200 sq ft",
    tags: ["Beach Access", "Private Elevator", "Terrace"],
    developer: "Nakheel",
    completion: "Q3 2026",
    roi: "10.2%"
  },
  {
    id: 6,
    name: "Investment Studio",
    location: "Dubai Marina",
    priceRange: "AED 980,000",
    status: "Ready",
    image: "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80",
    bedrooms: 0,
    bathrooms: 1,
    size: "480 sq ft",
    tags: ["Furnished", "Rental Guarantee"],
    developer: "DAMAC Properties",
    completion: "Completed",
    roi: "8.9%"
  }
];

interface PropertyListingsProps {
  onProjectSelect?: (project: any) => void;
  onLoadMore?: () => void;
}

export function PropertyListings({ onProjectSelect, onLoadMore }: PropertyListingsProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('price-low');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Ready':
        return 'bg-emerald-green text-white';
      case 'Off-plan':
        return 'bg-gold text-soft-brown';
      case 'Under Construction':
        return 'bg-orange-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const PropertyCard = ({ property }: { property: typeof properties[0] }) => {
    if (viewMode === 'list') {
      return (
        <Card className="overflow-hidden rounded-2xl shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] transition-all duration-300 hover:-translate-y-1 border-0 group">
          <div className="flex flex-col lg:flex-row">
            <div className="lg:w-2/5 relative">
              <ImageWithFallback
                src={property.image}
                alt={property.name}
                className="w-full h-64 lg:h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <Badge className={`absolute top-4 left-4 ${getStatusColor(property.status)}`}>
                {property.status}
              </Badge>
              <div className="absolute top-4 right-4">
                <Button variant="ghost" size="sm" className="bg-white/90 hover:bg-white text-soft-brown p-2">
                  <Heart className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <CardContent className="lg:w-3/5 p-8">
              <div className="flex flex-col h-full justify-between">
                <div>
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-soft-brown mb-2 group-hover:text-gold transition-colors">{property.name}</h3>
                      <div className="flex items-center text-warm-gray mb-3">
                        <MapPin className="w-4 h-4 mr-2" />
                        <span>{property.location}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl text-soft-brown mb-1">{property.priceRange}</div>
                      <div className="flex items-center text-emerald-500 text-sm">
                        <TrendingUp className="w-3 h-3 mr-1" />
                        {property.roi} ROI
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-6 mb-6 text-warm-gray">
                    {property.bedrooms > 0 && (
                      <div className="flex items-center">
                        <Bed className="w-4 h-4 mr-2 text-gold" />
                        <span>{property.bedrooms} BR</span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <Bath className="w-4 h-4 mr-2 text-gold" />
                      <span>{property.bathrooms} Bath</span>
                    </div>
                    <div className="flex items-center">
                      <Square className="w-4 h-4 mr-2 text-gold" />
                      <span>{property.size}</span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-6">
                    {property.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="bg-beige text-soft-brown text-xs px-3 py-1">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-soft-gray/30">
                  <div className="text-warm-gray">
                    <div className="text-sm">by {property.developer}</div>
                    <div className="text-xs">Completion: {property.completion}</div>
                  </div>
                  <Button 
                    className="bg-gold hover:bg-soft-brown text-soft-brown hover:text-white px-6 py-2 transition-all duration-300"
                    onClick={() => onProjectSelect?.(property)}
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </CardContent>
          </div>
        </Card>
      );
    }

    return (
      <Card className="overflow-hidden rounded-2xl shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] transition-all duration-300 hover:-translate-y-1 border-0 group cursor-pointer">
        <div className="relative">
          <ImageWithFallback
            src={property.image}
            alt={property.name}
            className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <Badge className={`absolute top-4 left-4 ${getStatusColor(property.status)}`}>
            {property.status}
          </Badge>
          <div className="absolute top-4 right-4">
            <Button variant="ghost" size="sm" className="bg-white/90 hover:bg-white text-soft-brown p-2">
              <Heart className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Price Overlay */}
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg">
              <div className="flex items-center justify-between">
                <div className="text-2xl text-soft-brown text-[16px] text-[20px] font-bold">{property.priceRange}</div>
                <div className="flex items-center text-emerald-500 text-sm">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  {property.roi} ROI
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <CardContent className="p-6">
          <div className="mb-4">
            <h4 className="text-soft-brown mb-2 group-hover:text-gold transition-colors">{property.name}</h4>
            <div className="flex items-center text-warm-gray text-sm">
              <MapPin className="w-4 h-4 mr-1" />
              <span>{property.location}</span>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-3 mb-4 text-sm text-warm-gray">
            {property.bedrooms > 0 && (
              <div className="flex items-center">
                <Bed className="w-4 h-4 mr-1 text-gold" />
                {property.bedrooms}
              </div>
            )}
            <div className="flex items-center">
              <Bath className="w-4 h-4 mr-1 text-gold" />
              {property.bathrooms}
            </div>
            <div className="flex items-center">
              <Square className="w-4 h-4 mr-1 text-gold" />
              {property.size}
            </div>
          </div>

          <div className="flex flex-wrap gap-1 mb-4">
            {property.tags.slice(0, 2).map((tag) => (
              <Badge key={tag} variant="secondary" className="bg-beige text-soft-brown text-xs">
                {tag}
              </Badge>
            ))}
          </div>

          <div className="flex items-center justify-between pt-4 border-t border-soft-gray/30">
            <div className="text-warm-gray text-sm">
              <div>by {property.developer}</div>
              <div className="text-xs">{property.completion}</div>
            </div>
            <Button 
              className="bg-gold hover:bg-soft-brown text-soft-brown hover:text-white px-4 py-2 text-sm transition-all duration-300 text-[13px]"
              onClick={() => onProjectSelect?.(property)}
            >
              View Details
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <section className="section-padding bg-beige">
      <div className="container">
        
        {/* Section Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-12">
          <div className="mb-6 lg:mb-0">
            <h2 className="mb-4 text-soft-brown text-[36px]">Exclusive Property Listings</h2>
            <p className="text-warm-gray text-xl max-w-2xl leading-relaxed">
              Discover premium investment opportunities with guaranteed returns and world-class amenities
            </p>
          </div>
          
          {/* Controls */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            {/* Sort Dropdown */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-52 rounded-xl bg-white border-soft-gray/30 text-soft-brown">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="price-low">Price: Low to High</SelectItem>
                <SelectItem value="price-high">Price: High to Low</SelectItem>
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="size-large">Size: Largest First</SelectItem>
                <SelectItem value="roi">Best ROI</SelectItem>
              </SelectContent>
            </Select>

            {/* View Toggle */}
            <div className="flex bg-white border border-soft-gray/30 rounded-xl overflow-hidden">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className={`rounded-none px-4 py-2 ${
                  viewMode === 'grid' 
                    ? 'bg-gold text-soft-brown hover:bg-gold/90' 
                    : 'text-warm-gray hover:text-soft-brown'
                }`}
              >
                <Grid3X3 className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className={`rounded-none px-4 py-2 ${
                  viewMode === 'list' 
                    ? 'bg-gold text-soft-brown hover:bg-gold/90' 
                    : 'text-warm-gray hover:text-soft-brown'
                }`}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Results Info */}
        <div className="mb-8">
          <div className="bg-white rounded-xl p-4 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between">
              <p className="text-warm-gray mb-2 sm:mb-0">
                Showing <span className="text-soft-brown">{properties.length} properties</span> • Last updated 2 hours ago
              </p>
              <div className="text-sm text-warm-gray">
                Average ROI: <span className="text-emerald-500">8.3%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Properties Grid/List */}
        <div className={viewMode === 'grid' 
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" 
          : "space-y-8"
        }>
          {properties.map((property) => (
            <PropertyCard key={property.id} property={property} />
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-16">
          <Button 
            variant="outline" 
            size="lg" 
            className="border-soft-brown text-soft-brown hover:bg-soft-brown hover:text-white px-8 py-3 rounded-xl transition-all duration-300"
            onClick={onLoadMore}
          >
            View All Properties
          </Button>
        </div>

        {/* Investment Stats */}
        <div className="mt-16 bg-white rounded-2xl p-8 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]">
          <div className="text-center mb-8">
            <h3 className="text-soft-brown mb-4">Investment Highlights</h3>
            <p className="text-warm-gray text-lg">Why Dubai properties are the smart choice for investors</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center group">
              <div className="text-3xl text-gold mb-2 group-hover:scale-110 transition-transform duration-300">8.3%</div>
              <div className="text-warm-gray">Average ROI</div>
            </div>
            <div className="text-center group">
              <div className="text-3xl text-gold mb-2 group-hover:scale-110 transition-transform duration-300">15%</div>
              <div className="text-warm-gray">Capital Appreciation</div>
            </div>
            <div className="text-center group">
              <div className="text-3xl text-gold mb-2 group-hover:scale-110 transition-transform duration-300">0%</div>
              <div className="text-warm-gray">Property Tax</div>
            </div>
            <div className="text-center group">
              <div className="text-3xl text-gold mb-2 group-hover:scale-110 transition-transform duration-300">100%</div>
              <div className="text-warm-gray">Foreign Ownership</div>
            </div>
          </div>
        </div>

      </div>
    </section>
  );
}