"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/canvg";
exports.ids = ["vendor-chunks/canvg"];
exports.modules = {

/***/ "(ssr)/./node_modules/canvg/lib/index.es.js":
/*!********************************************!*\
  !*** ./node_modules/canvg/lib/index.es.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AElement: () => (/* binding */ AElement),\n/* harmony export */   AnimateColorElement: () => (/* binding */ AnimateColorElement),\n/* harmony export */   AnimateElement: () => (/* binding */ AnimateElement),\n/* harmony export */   AnimateTransformElement: () => (/* binding */ AnimateTransformElement),\n/* harmony export */   BoundingBox: () => (/* binding */ BoundingBox),\n/* harmony export */   CB1: () => (/* binding */ CB1),\n/* harmony export */   CB2: () => (/* binding */ CB2),\n/* harmony export */   CB3: () => (/* binding */ CB3),\n/* harmony export */   CB4: () => (/* binding */ CB4),\n/* harmony export */   Canvg: () => (/* binding */ Canvg),\n/* harmony export */   CircleElement: () => (/* binding */ CircleElement),\n/* harmony export */   ClipPathElement: () => (/* binding */ ClipPathElement),\n/* harmony export */   DefsElement: () => (/* binding */ DefsElement),\n/* harmony export */   DescElement: () => (/* binding */ DescElement),\n/* harmony export */   Document: () => (/* binding */ Document),\n/* harmony export */   Element: () => (/* binding */ Element),\n/* harmony export */   EllipseElement: () => (/* binding */ EllipseElement),\n/* harmony export */   FeColorMatrixElement: () => (/* binding */ FeColorMatrixElement),\n/* harmony export */   FeCompositeElement: () => (/* binding */ FeCompositeElement),\n/* harmony export */   FeDropShadowElement: () => (/* binding */ FeDropShadowElement),\n/* harmony export */   FeGaussianBlurElement: () => (/* binding */ FeGaussianBlurElement),\n/* harmony export */   FeMorphologyElement: () => (/* binding */ FeMorphologyElement),\n/* harmony export */   FilterElement: () => (/* binding */ FilterElement),\n/* harmony export */   Font: () => (/* binding */ Font),\n/* harmony export */   FontElement: () => (/* binding */ FontElement),\n/* harmony export */   FontFaceElement: () => (/* binding */ FontFaceElement),\n/* harmony export */   GElement: () => (/* binding */ GElement),\n/* harmony export */   GlyphElement: () => (/* binding */ GlyphElement),\n/* harmony export */   GradientElement: () => (/* binding */ GradientElement),\n/* harmony export */   ImageElement: () => (/* binding */ ImageElement),\n/* harmony export */   LineElement: () => (/* binding */ LineElement),\n/* harmony export */   LinearGradientElement: () => (/* binding */ LinearGradientElement),\n/* harmony export */   MarkerElement: () => (/* binding */ MarkerElement),\n/* harmony export */   MaskElement: () => (/* binding */ MaskElement),\n/* harmony export */   Matrix: () => (/* binding */ Matrix),\n/* harmony export */   MissingGlyphElement: () => (/* binding */ MissingGlyphElement),\n/* harmony export */   Mouse: () => (/* binding */ Mouse),\n/* harmony export */   PSEUDO_ZERO: () => (/* binding */ PSEUDO_ZERO),\n/* harmony export */   Parser: () => (/* binding */ Parser),\n/* harmony export */   PathElement: () => (/* binding */ PathElement),\n/* harmony export */   PathParser: () => (/* binding */ PathParser),\n/* harmony export */   PatternElement: () => (/* binding */ PatternElement),\n/* harmony export */   Point: () => (/* binding */ Point),\n/* harmony export */   PolygonElement: () => (/* binding */ PolygonElement),\n/* harmony export */   PolylineElement: () => (/* binding */ PolylineElement),\n/* harmony export */   Property: () => (/* binding */ Property),\n/* harmony export */   QB1: () => (/* binding */ QB1),\n/* harmony export */   QB2: () => (/* binding */ QB2),\n/* harmony export */   QB3: () => (/* binding */ QB3),\n/* harmony export */   RadialGradientElement: () => (/* binding */ RadialGradientElement),\n/* harmony export */   RectElement: () => (/* binding */ RectElement),\n/* harmony export */   RenderedElement: () => (/* binding */ RenderedElement),\n/* harmony export */   Rotate: () => (/* binding */ Rotate),\n/* harmony export */   SVGElement: () => (/* binding */ SVGElement),\n/* harmony export */   SVGFontLoader: () => (/* binding */ SVGFontLoader),\n/* harmony export */   Scale: () => (/* binding */ Scale),\n/* harmony export */   Screen: () => (/* binding */ Screen),\n/* harmony export */   Skew: () => (/* binding */ Skew),\n/* harmony export */   SkewX: () => (/* binding */ SkewX),\n/* harmony export */   SkewY: () => (/* binding */ SkewY),\n/* harmony export */   StopElement: () => (/* binding */ StopElement),\n/* harmony export */   StyleElement: () => (/* binding */ StyleElement),\n/* harmony export */   SymbolElement: () => (/* binding */ SymbolElement),\n/* harmony export */   TRefElement: () => (/* binding */ TRefElement),\n/* harmony export */   TSpanElement: () => (/* binding */ TSpanElement),\n/* harmony export */   TextElement: () => (/* binding */ TextElement),\n/* harmony export */   TextPathElement: () => (/* binding */ TextPathElement),\n/* harmony export */   TitleElement: () => (/* binding */ TitleElement),\n/* harmony export */   Transform: () => (/* binding */ Transform),\n/* harmony export */   Translate: () => (/* binding */ Translate),\n/* harmony export */   UnknownElement: () => (/* binding */ UnknownElement),\n/* harmony export */   UseElement: () => (/* binding */ UseElement),\n/* harmony export */   ViewPort: () => (/* binding */ ViewPort),\n/* harmony export */   compressSpaces: () => (/* binding */ compressSpaces),\n/* harmony export */   \"default\": () => (/* binding */ Canvg),\n/* harmony export */   getSelectorSpecificity: () => (/* binding */ getSelectorSpecificity),\n/* harmony export */   normalizeAttributeName: () => (/* binding */ normalizeAttributeName),\n/* harmony export */   normalizeColor: () => (/* binding */ normalizeColor),\n/* harmony export */   parseExternalUrl: () => (/* binding */ parseExternalUrl),\n/* harmony export */   presets: () => (/* binding */ index),\n/* harmony export */   toNumbers: () => (/* binding */ toNumbers),\n/* harmony export */   trimLeft: () => (/* binding */ trimLeft),\n/* harmony export */   trimRight: () => (/* binding */ trimRight),\n/* harmony export */   vectorMagnitude: () => (/* binding */ vectorMagnitude),\n/* harmony export */   vectorsAngle: () => (/* binding */ vectorsAngle),\n/* harmony export */   vectorsRatio: () => (/* binding */ vectorsRatio)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.promise.js */ \"(ssr)/./node_modules/core-js/modules/es.promise.js\");\n/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\");\n/* harmony import */ var core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.string.match.js */ \"(ssr)/./node_modules/core-js/modules/es.string.match.js\");\n/* harmony import */ var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.string.replace.js */ \"(ssr)/./node_modules/core-js/modules/es.string.replace.js\");\n/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.string.starts-with.js */ \"(ssr)/./node_modules/core-js/modules/es.string.starts-with.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"(ssr)/./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"(ssr)/./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var core_js_modules_es_array_reduce_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.array.reduce.js */ \"(ssr)/./node_modules/core-js/modules/es.array.reduce.js\");\n/* harmony import */ var core_js_modules_es_string_ends_with_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.string.ends-with.js */ \"(ssr)/./node_modules/core-js/modules/es.string.ends-with.js\");\n/* harmony import */ var core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.string.split.js */ \"(ssr)/./node_modules/core-js/modules/es.string.split.js\");\n/* harmony import */ var raf__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! raf */ \"(ssr)/./node_modules/raf/index.js\");\n/* harmony import */ var core_js_modules_es_string_trim_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.string.trim.js */ \"(ssr)/./node_modules/core-js/modules/es.string.trim.js\");\n/* harmony import */ var rgbcolor__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rgbcolor */ \"(ssr)/./node_modules/rgbcolor/index.js\");\n/* harmony import */ var core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.array.index-of.js */ \"(ssr)/./node_modules/core-js/modules/es.array.index-of.js\");\n/* harmony import */ var core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/es.string.includes.js */ \"(ssr)/./node_modules/core-js/modules/es.string.includes.js\");\n/* harmony import */ var core_js_modules_es_array_reverse_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/es.array.reverse.js */ \"(ssr)/./node_modules/core-js/modules/es.array.reverse.js\");\n/* harmony import */ var svg_pathdata__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! svg-pathdata */ \"(ssr)/./node_modules/svg-pathdata/lib/SVGPathData.module.js\");\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ \"(ssr)/./node_modules/core-js/modules/es.regexp.to-string.js\");\n/* harmony import */ var stackblur_canvas__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! stackblur-canvas */ \"(ssr)/./node_modules/stackblur-canvas/dist/stackblur-es.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\r\n * Options preset for `OffscreenCanvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @returns Preset object.\r\n */\nfunction offscreen() {\n  var {\n    DOMParser: DOMParserFallback\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var preset = {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser: DOMParserFallback,\n\n    createCanvas(width, height) {\n      return new OffscreenCanvas(width, height);\n    },\n\n    createImage(url) {\n      return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n        var response = yield fetch(url);\n        var blob = yield response.blob();\n        var img = yield createImageBitmap(blob);\n        return img;\n      })();\n    }\n\n  };\n\n  if (typeof DOMParser !== 'undefined' || typeof DOMParserFallback === 'undefined') {\n    Reflect.deleteProperty(preset, 'DOMParser');\n  }\n\n  return preset;\n}\n\n/**\r\n * Options preset for `node-canvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @param config.canvas - `node-canvas` exports.\r\n * @param config.fetch - WHATWG-compatible `fetch` function.\r\n * @returns Preset object.\r\n */\nfunction node(_ref) {\n  var {\n    DOMParser,\n    canvas,\n    fetch\n  } = _ref;\n  return {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser,\n    fetch,\n    createCanvas: canvas.createCanvas,\n    createImage: canvas.loadImage\n  };\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\toffscreen: offscreen,\n\tnode: node\n});\n\n/**\r\n * HTML-safe compress white-spaces.\r\n * @param str - String to compress.\r\n * @returns String.\r\n */\nfunction compressSpaces(str) {\n  return str.replace(/(?!\\u3000)\\s+/gm, ' ');\n}\n/**\r\n * HTML-safe left trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimLeft(str) {\n  return str.replace(/^[\\n \\t]+/, '');\n}\n/**\r\n * HTML-safe right trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimRight(str) {\n  return str.replace(/[\\n \\t]+$/, '');\n}\n/**\r\n * String to numbers array.\r\n * @param str - Numbers string.\r\n * @returns Numbers array.\r\n */\n\nfunction toNumbers(str) {\n  var matches = (str || '').match(/-?(\\d+(?:\\.\\d*(?:[eE][+-]?\\d+)?)?|\\.\\d+)(?=\\D|$)/gm) || [];\n  return matches.map(parseFloat);\n} // Microsoft Edge fix\n\nvar allUppercase = /^[A-Z-]+$/;\n/**\r\n * Normalize attribute name.\r\n * @param name - Attribute name.\r\n * @returns Normalized attribute name.\r\n */\n\nfunction normalizeAttributeName(name) {\n  if (allUppercase.test(name)) {\n    return name.toLowerCase();\n  }\n\n  return name;\n}\n/**\r\n * Parse external URL.\r\n * @param url - CSS url string.\r\n * @returns Parsed URL.\r\n */\n\nfunction parseExternalUrl(url) {\n  //                      single quotes [2]\n  //                      v         double quotes [3]\n  //                      v         v         no quotes [4]\n  //                      v         v         v\n  var urlMatch = /url\\(('([^']+)'|\"([^\"]+)\"|([^'\")]+))\\)/.exec(url) || [];\n  return urlMatch[2] || urlMatch[3] || urlMatch[4];\n}\n/**\r\n * Transform floats to integers in rgb colors.\r\n * @param color - Color to normalize.\r\n * @returns Normalized color.\r\n */\n\nfunction normalizeColor(color) {\n  if (!color.startsWith('rgb')) {\n    return color;\n  }\n\n  var rgbParts = 3;\n  var normalizedColor = color.replace(/\\d+(\\.\\d+)?/g, (num, isFloat) => rgbParts-- && isFloat ? String(Math.round(parseFloat(num))) : num);\n  return normalizedColor;\n}\n\n// slightly modified version of https://github.com/keeganstreet/specificity/blob/master/specificity.js\nvar attributeRegex = /(\\[[^\\]]+\\])/g;\nvar idRegex = /(#[^\\s+>~.[:]+)/g;\nvar classRegex = /(\\.[^\\s+>~.[:]+)/g;\nvar pseudoElementRegex = /(::[^\\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi;\nvar pseudoClassWithBracketsRegex = /(:[\\w-]+\\([^)]*\\))/gi;\nvar pseudoClassRegex = /(:[^\\s+>~.[:]+)/g;\nvar elementRegex = /([^\\s+>~.[:]+)/g;\n\nfunction findSelectorMatch(selector, regex) {\n  var matches = regex.exec(selector);\n\n  if (!matches) {\n    return [selector, 0];\n  }\n\n  return [selector.replace(regex, ' '), matches.length];\n}\n/**\r\n * Measure selector specificity.\r\n * @param selector - Selector to measure.\r\n * @returns Specificity.\r\n */\n\n\nfunction getSelectorSpecificity(selector) {\n  var specificity = [0, 0, 0];\n  var currentSelector = selector.replace(/:not\\(([^)]*)\\)/g, '     $1 ').replace(/{[\\s\\S]*/gm, ' ');\n  var delta = 0;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, attributeRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, idRegex);\n  specificity[0] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, classRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoElementRegex);\n  specificity[2] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassWithBracketsRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassRegex);\n  specificity[1] += delta;\n  currentSelector = currentSelector.replace(/[*\\s+>~]/g, ' ').replace(/[#.]/g, ' ');\n  [currentSelector, delta] = findSelectorMatch(currentSelector, elementRegex); // lgtm [js/useless-assignment-to-local]\n\n  specificity[2] += delta;\n  return specificity.join('');\n}\n\nvar PSEUDO_ZERO = .00000001;\n/**\r\n * Vector magnitude.\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorMagnitude(v) {\n  return Math.sqrt(Math.pow(v[0], 2) + Math.pow(v[1], 2));\n}\n/**\r\n * Ratio between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsRatio(u, v) {\n  return (u[0] * v[0] + u[1] * v[1]) / (vectorMagnitude(u) * vectorMagnitude(v));\n}\n/**\r\n * Angle between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsAngle(u, v) {\n  return (u[0] * v[1] < u[1] * v[0] ? -1 : 1) * Math.acos(vectorsRatio(u, v));\n}\nfunction CB1(t) {\n  return t * t * t;\n}\nfunction CB2(t) {\n  return 3 * t * t * (1 - t);\n}\nfunction CB3(t) {\n  return 3 * t * (1 - t) * (1 - t);\n}\nfunction CB4(t) {\n  return (1 - t) * (1 - t) * (1 - t);\n}\nfunction QB1(t) {\n  return t * t;\n}\nfunction QB2(t) {\n  return 2 * t * (1 - t);\n}\nfunction QB3(t) {\n  return (1 - t) * (1 - t);\n}\n\nclass Property {\n  constructor(document, name, value) {\n    this.document = document;\n    this.name = name;\n    this.value = value;\n    this.isNormalizedColor = false;\n  }\n\n  static empty(document) {\n    return new Property(document, 'EMPTY', '');\n  }\n\n  split() {\n    var separator = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ' ';\n    var {\n      document,\n      name\n    } = this;\n    return compressSpaces(this.getString()).trim().split(separator).map(value => new Property(document, name, value));\n  }\n\n  hasValue(zeroIsValue) {\n    var {\n      value\n    } = this;\n    return value !== null && value !== '' && (zeroIsValue || value !== 0) && typeof value !== 'undefined';\n  }\n\n  isString(regexp) {\n    var {\n      value\n    } = this;\n    var result = typeof value === 'string';\n\n    if (!result || !regexp) {\n      return result;\n    }\n\n    return regexp.test(value);\n  }\n\n  isUrlDefinition() {\n    return this.isString(/^url\\(/);\n  }\n\n  isPixels() {\n    if (!this.hasValue()) {\n      return false;\n    }\n\n    var asString = this.getString();\n\n    switch (true) {\n      case asString.endsWith('px'):\n      case /^[0-9]+$/.test(asString):\n        return true;\n\n      default:\n        return false;\n    }\n  }\n\n  setValue(value) {\n    this.value = value;\n    return this;\n  }\n\n  getValue(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return this.value;\n    }\n\n    return def;\n  }\n\n  getNumber(def) {\n    if (!this.hasValue()) {\n      if (typeof def === 'undefined') {\n        return 0;\n      }\n\n      return parseFloat(def);\n    }\n\n    var {\n      value\n    } = this;\n    var n = parseFloat(value);\n\n    if (this.isString(/%$/)) {\n      n /= 100.0;\n    }\n\n    return n;\n  }\n\n  getString(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return typeof this.value === 'undefined' ? '' : String(this.value);\n    }\n\n    return String(def);\n  }\n\n  getColor(def) {\n    var color = this.getString(def);\n\n    if (this.isNormalizedColor) {\n      return color;\n    }\n\n    this.isNormalizedColor = true;\n    color = normalizeColor(color);\n    this.value = color;\n    return color;\n  }\n\n  getDpi() {\n    return 96.0; // TODO: compute?\n  }\n\n  getRem() {\n    return this.document.rootEmSize;\n  }\n\n  getEm() {\n    return this.document.emSize;\n  }\n\n  getUnits() {\n    return this.getString().replace(/[0-9.-]/g, '');\n  }\n\n  getPixels(axisOrIsFontSize) {\n    var processPercent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    var [axis, isFontSize] = typeof axisOrIsFontSize === 'boolean' ? [undefined, axisOrIsFontSize] : [axisOrIsFontSize];\n    var {\n      viewPort\n    } = this.document.screen;\n\n    switch (true) {\n      case this.isString(/vmin$/):\n        return this.getNumber() / 100.0 * Math.min(viewPort.computeSize('x'), viewPort.computeSize('y'));\n\n      case this.isString(/vmax$/):\n        return this.getNumber() / 100.0 * Math.max(viewPort.computeSize('x'), viewPort.computeSize('y'));\n\n      case this.isString(/vw$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('x');\n\n      case this.isString(/vh$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('y');\n\n      case this.isString(/rem$/):\n        return this.getNumber() * this.getRem();\n\n      case this.isString(/em$/):\n        return this.getNumber() * this.getEm();\n\n      case this.isString(/ex$/):\n        return this.getNumber() * this.getEm() / 2.0;\n\n      case this.isString(/px$/):\n        return this.getNumber();\n\n      case this.isString(/pt$/):\n        return this.getNumber() * this.getDpi() * (1.0 / 72.0);\n\n      case this.isString(/pc$/):\n        return this.getNumber() * 15;\n\n      case this.isString(/cm$/):\n        return this.getNumber() * this.getDpi() / 2.54;\n\n      case this.isString(/mm$/):\n        return this.getNumber() * this.getDpi() / 25.4;\n\n      case this.isString(/in$/):\n        return this.getNumber() * this.getDpi();\n\n      case this.isString(/%$/) && isFontSize:\n        return this.getNumber() * this.getEm();\n\n      case this.isString(/%$/):\n        return this.getNumber() * viewPort.computeSize(axis);\n\n      default:\n        {\n          var n = this.getNumber();\n\n          if (processPercent && n < 1.0) {\n            return n * viewPort.computeSize(axis);\n          }\n\n          return n;\n        }\n    }\n  }\n\n  getMilliseconds() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    if (this.isString(/ms$/)) {\n      return this.getNumber();\n    }\n\n    return this.getNumber() * 1000;\n  }\n\n  getRadians() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    switch (true) {\n      case this.isString(/deg$/):\n        return this.getNumber() * (Math.PI / 180.0);\n\n      case this.isString(/grad$/):\n        return this.getNumber() * (Math.PI / 200.0);\n\n      case this.isString(/rad$/):\n        return this.getNumber();\n\n      default:\n        return this.getNumber() * (Math.PI / 180.0);\n    }\n  }\n\n  getDefinition() {\n    var asString = this.getString();\n    var name = /#([^)'\"]+)/.exec(asString);\n\n    if (name) {\n      name = name[1];\n    }\n\n    if (!name) {\n      name = asString;\n    }\n\n    return this.document.definitions[name];\n  }\n\n  getFillStyleDefinition(element, opacity) {\n    var def = this.getDefinition();\n\n    if (!def) {\n      return null;\n    } // gradient\n\n\n    if (typeof def.createGradient === 'function') {\n      return def.createGradient(this.document.ctx, element, opacity);\n    } // pattern\n\n\n    if (typeof def.createPattern === 'function') {\n      if (def.getHrefAttribute().hasValue()) {\n        var patternTransform = def.getAttribute('patternTransform');\n        def = def.getHrefAttribute().getDefinition();\n\n        if (patternTransform.hasValue()) {\n          def.getAttribute('patternTransform', true).setValue(patternTransform.value);\n        }\n      }\n\n      return def.createPattern(this.document.ctx, element, opacity);\n    }\n\n    return null;\n  }\n\n  getTextBaseline() {\n    if (!this.hasValue()) {\n      return null;\n    }\n\n    return Property.textBaselineMapping[this.getString()];\n  }\n\n  addOpacity(opacity) {\n    var value = this.getColor();\n    var len = value.length;\n    var commas = 0; // Simulate old RGBColor version, which can't parse rgba.\n\n    for (var i = 0; i < len; i++) {\n      if (value[i] === ',') {\n        commas++;\n      }\n\n      if (commas === 3) {\n        break;\n      }\n    }\n\n    if (opacity.hasValue() && this.isString() && commas !== 3) {\n      var color = new rgbcolor__WEBPACK_IMPORTED_MODULE_13__(value);\n\n      if (color.ok) {\n        color.alpha = opacity.getNumber();\n        value = color.toRGBA();\n      }\n    }\n\n    return new Property(this.document, this.name, value);\n  }\n\n}\nProperty.textBaselineMapping = {\n  'baseline': 'alphabetic',\n  'before-edge': 'top',\n  'text-before-edge': 'top',\n  'middle': 'middle',\n  'central': 'middle',\n  'after-edge': 'bottom',\n  'text-after-edge': 'bottom',\n  'ideographic': 'ideographic',\n  'alphabetic': 'alphabetic',\n  'hanging': 'hanging',\n  'mathematical': 'alphabetic'\n};\n\nclass ViewPort {\n  constructor() {\n    this.viewPorts = [];\n  }\n\n  clear() {\n    this.viewPorts = [];\n  }\n\n  setCurrent(width, height) {\n    this.viewPorts.push({\n      width,\n      height\n    });\n  }\n\n  removeCurrent() {\n    this.viewPorts.pop();\n  }\n\n  getCurrent() {\n    var {\n      viewPorts\n    } = this;\n    return viewPorts[viewPorts.length - 1];\n  }\n\n  get width() {\n    return this.getCurrent().width;\n  }\n\n  get height() {\n    return this.getCurrent().height;\n  }\n\n  computeSize(d) {\n    if (typeof d === 'number') {\n      return d;\n    }\n\n    if (d === 'x') {\n      return this.width;\n    }\n\n    if (d === 'y') {\n      return this.height;\n    }\n\n    return Math.sqrt(Math.pow(this.width, 2) + Math.pow(this.height, 2)) / Math.sqrt(2);\n  }\n\n}\n\nclass Point {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n\n  static parse(point) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var [x = defaultValue, y = defaultValue] = toNumbers(point);\n    return new Point(x, y);\n  }\n\n  static parseScale(scale) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var [x = defaultValue, y = x] = toNumbers(scale);\n    return new Point(x, y);\n  }\n\n  static parsePath(path) {\n    var points = toNumbers(path);\n    var len = points.length;\n    var pathPoints = [];\n\n    for (var i = 0; i < len; i += 2) {\n      pathPoints.push(new Point(points[i], points[i + 1]));\n    }\n\n    return pathPoints;\n  }\n\n  angleTo(point) {\n    return Math.atan2(point.y - this.y, point.x - this.x);\n  }\n\n  applyTransform(transform) {\n    var {\n      x,\n      y\n    } = this;\n    var xp = x * transform[0] + y * transform[2] + transform[4];\n    var yp = x * transform[1] + y * transform[3] + transform[5];\n    this.x = xp;\n    this.y = yp;\n  }\n\n}\n\nclass Mouse {\n  constructor(screen) {\n    this.screen = screen;\n    this.working = false;\n    this.events = [];\n    this.eventElements = []; // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onClick = this.onClick.bind(this); // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onMouseMove = this.onMouseMove.bind(this);\n  }\n\n  isWorking() {\n    return this.working;\n  }\n\n  start() {\n    if (this.working) {\n      return;\n    }\n\n    var {\n      screen,\n      onClick,\n      onMouseMove\n    } = this;\n    var canvas = screen.ctx.canvas;\n    canvas.onclick = onClick;\n    canvas.onmousemove = onMouseMove;\n    this.working = true;\n  }\n\n  stop() {\n    if (!this.working) {\n      return;\n    }\n\n    var canvas = this.screen.ctx.canvas;\n    this.working = false;\n    canvas.onclick = null;\n    canvas.onmousemove = null;\n  }\n\n  hasEvents() {\n    return this.working && this.events.length > 0;\n  }\n\n  runEvents() {\n    if (!this.working) {\n      return;\n    }\n\n    var {\n      screen: document,\n      events,\n      eventElements\n    } = this;\n    var {\n      style\n    } = document.ctx.canvas;\n\n    if (style) {\n      style.cursor = '';\n    }\n\n    events.forEach((_ref, i) => {\n      var {\n        run\n      } = _ref;\n      var element = eventElements[i];\n\n      while (element) {\n        run(element);\n        element = element.parent;\n      }\n    }); // done running, clear\n\n    this.events = [];\n    this.eventElements = [];\n  }\n\n  checkPath(element, ctx) {\n    if (!this.working || !ctx) {\n      return;\n    }\n\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref2, i) => {\n      var {\n        x,\n        y\n      } = _ref2;\n\n      if (!eventElements[i] && ctx.isPointInPath && ctx.isPointInPath(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n\n  checkBoundingBox(element, boundingBox) {\n    if (!this.working || !boundingBox) {\n      return;\n    }\n\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref3, i) => {\n      var {\n        x,\n        y\n      } = _ref3;\n\n      if (!eventElements[i] && boundingBox.isPointInBox(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n\n  mapXY(x, y) {\n    var {\n      window,\n      ctx\n    } = this.screen;\n    var point = new Point(x, y);\n    var element = ctx.canvas;\n\n    while (element) {\n      point.x -= element.offsetLeft;\n      point.y -= element.offsetTop;\n      element = element.offsetParent;\n    }\n\n    if (window.scrollX) {\n      point.x += window.scrollX;\n    }\n\n    if (window.scrollY) {\n      point.y += window.scrollY;\n    }\n\n    return point;\n  }\n\n  onClick(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onclick',\n      x,\n      y,\n\n      run(eventTarget) {\n        if (eventTarget.onClick) {\n          eventTarget.onClick();\n        }\n      }\n\n    });\n  }\n\n  onMouseMove(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onmousemove',\n      x,\n      y,\n\n      run(eventTarget) {\n        if (eventTarget.onMouseMove) {\n          eventTarget.onMouseMove();\n        }\n      }\n\n    });\n  }\n\n}\n\nvar defaultWindow = typeof window !== 'undefined' ? window : null;\nvar defaultFetch$1 = typeof fetch !== 'undefined' ? fetch.bind(undefined) // `fetch` depends on context: `someObject.fetch(...)` will throw error.\n: null;\nclass Screen {\n  constructor(ctx) {\n    var {\n      fetch = defaultFetch$1,\n      window = defaultWindow\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.ctx = ctx;\n    this.FRAMERATE = 30;\n    this.MAX_VIRTUAL_PIXELS = 30000;\n    this.CLIENT_WIDTH = 800;\n    this.CLIENT_HEIGHT = 600;\n    this.viewPort = new ViewPort();\n    this.mouse = new Mouse(this);\n    this.animations = [];\n    this.waits = [];\n    this.frameDuration = 0;\n    this.isReadyLock = false;\n    this.isFirstRender = true;\n    this.intervalId = null;\n    this.window = window;\n    this.fetch = fetch;\n  }\n\n  wait(checker) {\n    this.waits.push(checker);\n  }\n\n  ready() {\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\n    if (!this.readyPromise) {\n      return Promise.resolve();\n    }\n\n    return this.readyPromise;\n  }\n\n  isReady() {\n    if (this.isReadyLock) {\n      return true;\n    }\n\n    var isReadyLock = this.waits.every(_ => _());\n\n    if (isReadyLock) {\n      this.waits = [];\n\n      if (this.resolveReady) {\n        this.resolveReady();\n      }\n    }\n\n    this.isReadyLock = isReadyLock;\n    return isReadyLock;\n  }\n\n  setDefaults(ctx) {\n    // initial values and defaults\n    ctx.strokeStyle = 'rgba(0,0,0,0)';\n    ctx.lineCap = 'butt';\n    ctx.lineJoin = 'miter';\n    ctx.miterLimit = 4;\n  }\n\n  setViewBox(_ref) {\n    var {\n      document,\n      ctx,\n      aspectRatio,\n      width,\n      desiredWidth,\n      height,\n      desiredHeight,\n      minX = 0,\n      minY = 0,\n      refX,\n      refY,\n      clip = false,\n      clipX = 0,\n      clipY = 0\n    } = _ref;\n    // aspect ratio - http://www.w3.org/TR/SVG/coords.html#PreserveAspectRatioAttribute\n    var cleanAspectRatio = compressSpaces(aspectRatio).replace(/^defer\\s/, ''); // ignore defer\n\n    var [aspectRatioAlign, aspectRatioMeetOrSlice] = cleanAspectRatio.split(' ');\n    var align = aspectRatioAlign || 'xMidYMid';\n    var meetOrSlice = aspectRatioMeetOrSlice || 'meet'; // calculate scale\n\n    var scaleX = width / desiredWidth;\n    var scaleY = height / desiredHeight;\n    var scaleMin = Math.min(scaleX, scaleY);\n    var scaleMax = Math.max(scaleX, scaleY);\n    var finalDesiredWidth = desiredWidth;\n    var finalDesiredHeight = desiredHeight;\n\n    if (meetOrSlice === 'meet') {\n      finalDesiredWidth *= scaleMin;\n      finalDesiredHeight *= scaleMin;\n    }\n\n    if (meetOrSlice === 'slice') {\n      finalDesiredWidth *= scaleMax;\n      finalDesiredHeight *= scaleMax;\n    }\n\n    var refXProp = new Property(document, 'refX', refX);\n    var refYProp = new Property(document, 'refY', refY);\n    var hasRefs = refXProp.hasValue() && refYProp.hasValue();\n\n    if (hasRefs) {\n      ctx.translate(-scaleMin * refXProp.getPixels('x'), -scaleMin * refYProp.getPixels('y'));\n    }\n\n    if (clip) {\n      var scaledClipX = scaleMin * clipX;\n      var scaledClipY = scaleMin * clipY;\n      ctx.beginPath();\n      ctx.moveTo(scaledClipX, scaledClipY);\n      ctx.lineTo(width, scaledClipY);\n      ctx.lineTo(width, height);\n      ctx.lineTo(scaledClipX, height);\n      ctx.closePath();\n      ctx.clip();\n    }\n\n    if (!hasRefs) {\n      var isMeetMinY = meetOrSlice === 'meet' && scaleMin === scaleY;\n      var isSliceMaxY = meetOrSlice === 'slice' && scaleMax === scaleY;\n      var isMeetMinX = meetOrSlice === 'meet' && scaleMin === scaleX;\n      var isSliceMaxX = meetOrSlice === 'slice' && scaleMax === scaleX;\n\n      if (align.startsWith('xMid') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width / 2.0 - finalDesiredWidth / 2.0, 0);\n      }\n\n      if (align.endsWith('YMid') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height / 2.0 - finalDesiredHeight / 2.0);\n      }\n\n      if (align.startsWith('xMax') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width - finalDesiredWidth, 0);\n      }\n\n      if (align.endsWith('YMax') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height - finalDesiredHeight);\n      }\n    } // scale\n\n\n    switch (true) {\n      case align === 'none':\n        ctx.scale(scaleX, scaleY);\n        break;\n\n      case meetOrSlice === 'meet':\n        ctx.scale(scaleMin, scaleMin);\n        break;\n\n      case meetOrSlice === 'slice':\n        ctx.scale(scaleMax, scaleMax);\n        break;\n    } // translate\n\n\n    ctx.translate(-minX, -minY);\n  }\n\n  start(element) {\n    var {\n      enableRedraw = false,\n      ignoreMouse = false,\n      ignoreAnimation = false,\n      ignoreDimensions = false,\n      ignoreClear = false,\n      forceRedraw,\n      scaleWidth,\n      scaleHeight,\n      offsetX,\n      offsetY\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var {\n      FRAMERATE,\n      mouse\n    } = this;\n    var frameDuration = 1000 / FRAMERATE;\n    this.frameDuration = frameDuration;\n    this.readyPromise = new Promise(resolve => {\n      this.resolveReady = resolve;\n    });\n\n    if (this.isReady()) {\n      this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n    }\n\n    if (!enableRedraw) {\n      return;\n    }\n\n    var now = Date.now();\n    var then = now;\n    var delta = 0;\n\n    var tick = () => {\n      now = Date.now();\n      delta = now - then;\n\n      if (delta >= frameDuration) {\n        then = now - delta % frameDuration;\n\n        if (this.shouldUpdate(ignoreAnimation, forceRedraw)) {\n          this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n          mouse.runEvents();\n        }\n      }\n\n      this.intervalId = raf__WEBPACK_IMPORTED_MODULE_11__(tick);\n    };\n\n    if (!ignoreMouse) {\n      mouse.start();\n    }\n\n    this.intervalId = raf__WEBPACK_IMPORTED_MODULE_11__(tick);\n  }\n\n  stop() {\n    if (this.intervalId) {\n      raf__WEBPACK_IMPORTED_MODULE_11__.cancel(this.intervalId);\n      this.intervalId = null;\n    }\n\n    this.mouse.stop();\n  }\n\n  shouldUpdate(ignoreAnimation, forceRedraw) {\n    // need update from animations?\n    if (!ignoreAnimation) {\n      var {\n        frameDuration\n      } = this;\n      var shouldUpdate = this.animations.reduce((shouldUpdate, animation) => animation.update(frameDuration) || shouldUpdate, false);\n\n      if (shouldUpdate) {\n        return true;\n      }\n    } // need update from redraw?\n\n\n    if (typeof forceRedraw === 'function' && forceRedraw()) {\n      return true;\n    }\n\n    if (!this.isReadyLock && this.isReady()) {\n      return true;\n    } // need update from mouse events?\n\n\n    if (this.mouse.hasEvents()) {\n      return true;\n    }\n\n    return false;\n  }\n\n  render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY) {\n    var {\n      CLIENT_WIDTH,\n      CLIENT_HEIGHT,\n      viewPort,\n      ctx,\n      isFirstRender\n    } = this;\n    var canvas = ctx.canvas;\n    viewPort.clear();\n\n    if (canvas.width && canvas.height) {\n      viewPort.setCurrent(canvas.width, canvas.height);\n    } else {\n      viewPort.setCurrent(CLIENT_WIDTH, CLIENT_HEIGHT);\n    }\n\n    var widthStyle = element.getStyle('width');\n    var heightStyle = element.getStyle('height');\n\n    if (!ignoreDimensions && (isFirstRender || typeof scaleWidth !== 'number' && typeof scaleHeight !== 'number')) {\n      // set canvas size\n      if (widthStyle.hasValue()) {\n        canvas.width = widthStyle.getPixels('x');\n\n        if (canvas.style) {\n          canvas.style.width = \"\".concat(canvas.width, \"px\");\n        }\n      }\n\n      if (heightStyle.hasValue()) {\n        canvas.height = heightStyle.getPixels('y');\n\n        if (canvas.style) {\n          canvas.style.height = \"\".concat(canvas.height, \"px\");\n        }\n      }\n    }\n\n    var cWidth = canvas.clientWidth || canvas.width;\n    var cHeight = canvas.clientHeight || canvas.height;\n\n    if (ignoreDimensions && widthStyle.hasValue() && heightStyle.hasValue()) {\n      cWidth = widthStyle.getPixels('x');\n      cHeight = heightStyle.getPixels('y');\n    }\n\n    viewPort.setCurrent(cWidth, cHeight);\n\n    if (typeof offsetX === 'number') {\n      element.getAttribute('x', true).setValue(offsetX);\n    }\n\n    if (typeof offsetY === 'number') {\n      element.getAttribute('y', true).setValue(offsetY);\n    }\n\n    if (typeof scaleWidth === 'number' || typeof scaleHeight === 'number') {\n      var viewBox = toNumbers(element.getAttribute('viewBox').getString());\n      var xRatio = 0;\n      var yRatio = 0;\n\n      if (typeof scaleWidth === 'number') {\n        var _widthStyle = element.getStyle('width');\n\n        if (_widthStyle.hasValue()) {\n          xRatio = _widthStyle.getPixels('x') / scaleWidth;\n        } else if (!isNaN(viewBox[2])) {\n          xRatio = viewBox[2] / scaleWidth;\n        }\n      }\n\n      if (typeof scaleHeight === 'number') {\n        var _heightStyle = element.getStyle('height');\n\n        if (_heightStyle.hasValue()) {\n          yRatio = _heightStyle.getPixels('y') / scaleHeight;\n        } else if (!isNaN(viewBox[3])) {\n          yRatio = viewBox[3] / scaleHeight;\n        }\n      }\n\n      if (!xRatio) {\n        xRatio = yRatio;\n      }\n\n      if (!yRatio) {\n        yRatio = xRatio;\n      }\n\n      element.getAttribute('width', true).setValue(scaleWidth);\n      element.getAttribute('height', true).setValue(scaleHeight);\n      var transformStyle = element.getStyle('transform', true, true);\n      transformStyle.setValue(\"\".concat(transformStyle.getString(), \" scale(\").concat(1.0 / xRatio, \", \").concat(1.0 / yRatio, \")\"));\n    } // clear and render\n\n\n    if (!ignoreClear) {\n      ctx.clearRect(0, 0, cWidth, cHeight);\n    }\n\n    element.render(ctx);\n\n    if (isFirstRender) {\n      this.isFirstRender = false;\n    }\n  }\n\n}\nScreen.defaultWindow = defaultWindow;\nScreen.defaultFetch = defaultFetch$1;\n\nvar {\n  defaultFetch\n} = Screen;\nvar DefaultDOMParser = typeof DOMParser !== 'undefined' ? DOMParser : null;\nclass Parser {\n  constructor() {\n    var {\n      fetch = defaultFetch,\n      DOMParser = DefaultDOMParser\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.fetch = fetch;\n    this.DOMParser = DOMParser;\n  }\n\n  parse(resource) {\n    var _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      if (resource.startsWith('<')) {\n        return _this.parseFromString(resource);\n      }\n\n      return _this.load(resource);\n    })();\n  }\n\n  parseFromString(xml) {\n    var parser = new this.DOMParser();\n\n    try {\n      return this.checkDocument(parser.parseFromString(xml, 'image/svg+xml'));\n    } catch (err) {\n      return this.checkDocument(parser.parseFromString(xml, 'text/xml'));\n    }\n  }\n\n  checkDocument(document) {\n    var parserError = document.getElementsByTagName('parsererror')[0];\n\n    if (parserError) {\n      throw new Error(parserError.textContent);\n    }\n\n    return document;\n  }\n\n  load(url) {\n    var _this2 = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var response = yield _this2.fetch(url);\n      var xml = yield response.text();\n      return _this2.parseFromString(xml);\n    })();\n  }\n\n}\n\nclass Translate {\n  constructor(_, point) {\n    this.type = 'translate';\n    this.point = null;\n    this.point = Point.parse(point);\n  }\n\n  apply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(x || 0.0, y || 0.0);\n  }\n\n  unapply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(-1.0 * x || 0.0, -1.0 * y || 0.0);\n  }\n\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.point;\n    point.applyTransform([1, 0, 0, 1, x || 0.0, y || 0.0]);\n  }\n\n}\n\nclass Rotate {\n  constructor(document, rotate, transformOrigin) {\n    this.type = 'rotate';\n    this.angle = null;\n    this.originX = null;\n    this.originY = null;\n    this.cx = 0;\n    this.cy = 0;\n    var numbers = toNumbers(rotate);\n    this.angle = new Property(document, 'angle', numbers[0]);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n    this.cx = numbers[1] || 0;\n    this.cy = numbers[2] || 0;\n  }\n\n  apply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(-1.0 * angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    var {\n      cx,\n      cy,\n      angle\n    } = this;\n    var rad = angle.getRadians();\n    point.applyTransform([1, 0, 0, 1, cx || 0.0, cy || 0.0 // this.p.y\n    ]);\n    point.applyTransform([Math.cos(rad), Math.sin(rad), -Math.sin(rad), Math.cos(rad), 0, 0]);\n    point.applyTransform([1, 0, 0, 1, -cx || 0.0, -cy || 0.0 // -this.p.y\n    ]);\n  }\n\n}\n\nclass Scale {\n  constructor(_, scale, transformOrigin) {\n    this.type = 'scale';\n    this.scale = null;\n    this.originX = null;\n    this.originY = null;\n    var scaleSize = Point.parseScale(scale); // Workaround for node-canvas\n\n    if (scaleSize.x === 0 || scaleSize.y === 0) {\n      scaleSize.x = PSEUDO_ZERO;\n      scaleSize.y = PSEUDO_ZERO;\n    }\n\n    this.scale = scaleSize;\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n\n  apply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(x, y || x);\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(1.0 / x, 1.0 / y || x);\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.scale;\n    point.applyTransform([x || 0.0, 0, 0, y || 0.0, 0, 0]);\n  }\n\n}\n\nclass Matrix {\n  constructor(_, matrix, transformOrigin) {\n    this.type = 'matrix';\n    this.matrix = [];\n    this.originX = null;\n    this.originY = null;\n    this.matrix = toNumbers(matrix);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n\n  apply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]);\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var a = matrix[0];\n    var b = matrix[2];\n    var c = matrix[4];\n    var d = matrix[1];\n    var e = matrix[3];\n    var f = matrix[5];\n    var g = 0.0;\n    var h = 0.0;\n    var i = 1.0;\n    var det = 1 / (a * (e * i - f * h) - b * (d * i - f * g) + c * (d * h - e * g));\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(det * (e * i - f * h), det * (f * g - d * i), det * (c * h - b * i), det * (a * i - c * g), det * (b * f - c * e), det * (c * d - a * f));\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    point.applyTransform(this.matrix);\n  }\n\n}\n\nclass Skew extends Matrix {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skew';\n    this.angle = null;\n    this.angle = new Property(document, 'angle', skew);\n  }\n\n}\n\nclass SkewX extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewX';\n    this.matrix = [1, 0, Math.tan(this.angle.getRadians()), 1, 0, 0];\n  }\n\n}\n\nclass SkewY extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewY';\n    this.matrix = [1, Math.tan(this.angle.getRadians()), 0, 1, 0, 0];\n  }\n\n}\n\nfunction parseTransforms(transform) {\n  return compressSpaces(transform).trim().replace(/\\)([a-zA-Z])/g, ') $1').replace(/\\)(\\s?,\\s?)/g, ') ').split(/\\s(?=[a-z])/);\n}\n\nfunction parseTransform(transform) {\n  var [type, value] = transform.split('(');\n  return [type.trim(), value.trim().replace(')', '')];\n}\n\nclass Transform {\n  constructor(document, transform, transformOrigin) {\n    this.document = document;\n    this.transforms = [];\n    var data = parseTransforms(transform);\n    data.forEach(transform => {\n      if (transform === 'none') {\n        return;\n      }\n\n      var [type, value] = parseTransform(transform);\n      var TransformType = Transform.transformTypes[type];\n\n      if (typeof TransformType !== 'undefined') {\n        this.transforms.push(new TransformType(this.document, value, transformOrigin));\n      }\n    });\n  }\n\n  static fromElement(document, element) {\n    var transformStyle = element.getStyle('transform', false, true);\n    var [transformOriginXProperty, transformOriginYProperty = transformOriginXProperty] = element.getStyle('transform-origin', false, true).split();\n    var transformOrigin = [transformOriginXProperty, transformOriginYProperty];\n\n    if (transformStyle.hasValue()) {\n      return new Transform(document, transformStyle.getString(), transformOrigin);\n    }\n\n    return null;\n  }\n\n  apply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = 0; i < len; i++) {\n      transforms[i].apply(ctx);\n    }\n  }\n\n  unapply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = len - 1; i >= 0; i--) {\n      transforms[i].unapply(ctx);\n    }\n  } // TODO: applyToPoint unused ... remove?\n\n\n  applyToPoint(point) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = 0; i < len; i++) {\n      transforms[i].applyToPoint(point);\n    }\n  }\n\n}\nTransform.transformTypes = {\n  translate: Translate,\n  rotate: Rotate,\n  scale: Scale,\n  matrix: Matrix,\n  skewX: SkewX,\n  skewY: SkewY\n};\n\nclass Element {\n  constructor(document, node) {\n    var captureTextNodes = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.document = document;\n    this.node = node;\n    this.captureTextNodes = captureTextNodes;\n    this.attributes = Object.create(null);\n    this.styles = Object.create(null);\n    this.stylesSpecificity = Object.create(null);\n    this.animationFrozen = false;\n    this.animationFrozenValue = '';\n    this.parent = null;\n    this.children = [];\n\n    if (!node || node.nodeType !== 1) {\n      // ELEMENT_NODE\n      return;\n    } // add attributes\n\n\n    Array.from(node.attributes).forEach(attribute => {\n      var nodeName = normalizeAttributeName(attribute.nodeName);\n      this.attributes[nodeName] = new Property(document, nodeName, attribute.value);\n    });\n    this.addStylesFromStyleDefinition(); // add inline styles\n\n    if (this.getAttribute('style').hasValue()) {\n      var styles = this.getAttribute('style').getString().split(';').map(_ => _.trim());\n      styles.forEach(style => {\n        if (!style) {\n          return;\n        }\n\n        var [name, value] = style.split(':').map(_ => _.trim());\n        this.styles[name] = new Property(document, name, value);\n      });\n    }\n\n    var {\n      definitions\n    } = document;\n    var id = this.getAttribute('id'); // add id\n\n    if (id.hasValue()) {\n      if (!definitions[id.getString()]) {\n        definitions[id.getString()] = this;\n      }\n    }\n\n    Array.from(node.childNodes).forEach(childNode => {\n      if (childNode.nodeType === 1) {\n        this.addChild(childNode); // ELEMENT_NODE\n      } else if (captureTextNodes && (childNode.nodeType === 3 || childNode.nodeType === 4)) {\n        var textNode = document.createTextNode(childNode);\n\n        if (textNode.getText().length > 0) {\n          this.addChild(textNode); // TEXT_NODE\n        }\n      }\n    });\n  }\n\n  getAttribute(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var attr = this.attributes[name];\n\n    if (!attr && createIfNotExists) {\n      var _attr = new Property(this.document, name, '');\n\n      this.attributes[name] = _attr;\n      return _attr;\n    }\n\n    return attr || Property.empty(this.document);\n  }\n\n  getHrefAttribute() {\n    for (var key in this.attributes) {\n      if (key === 'href' || key.endsWith(':href')) {\n        return this.attributes[key];\n      }\n    }\n\n    return Property.empty(this.document);\n  }\n\n  getStyle(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var skipAncestors = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var style = this.styles[name];\n\n    if (style) {\n      return style;\n    }\n\n    var attr = this.getAttribute(name);\n\n    if (attr !== null && attr !== void 0 && attr.hasValue()) {\n      this.styles[name] = attr; // move up to me to cache\n\n      return attr;\n    }\n\n    if (!skipAncestors) {\n      var {\n        parent\n      } = this;\n\n      if (parent) {\n        var parentStyle = parent.getStyle(name);\n\n        if (parentStyle !== null && parentStyle !== void 0 && parentStyle.hasValue()) {\n          return parentStyle;\n        }\n      }\n    }\n\n    if (createIfNotExists) {\n      var _style = new Property(this.document, name, '');\n\n      this.styles[name] = _style;\n      return _style;\n    }\n\n    return style || Property.empty(this.document);\n  }\n\n  render(ctx) {\n    // don't render display=none\n    // don't render visibility=hidden\n    if (this.getStyle('display').getString() === 'none' || this.getStyle('visibility').getString() === 'hidden') {\n      return;\n    }\n\n    ctx.save();\n\n    if (this.getStyle('mask').hasValue()) {\n      // mask\n      var mask = this.getStyle('mask').getDefinition();\n\n      if (mask) {\n        this.applyEffects(ctx);\n        mask.apply(ctx, this);\n      }\n    } else if (this.getStyle('filter').getValue('none') !== 'none') {\n      // filter\n      var filter = this.getStyle('filter').getDefinition();\n\n      if (filter) {\n        this.applyEffects(ctx);\n        filter.apply(ctx, this);\n      }\n    } else {\n      this.setContext(ctx);\n      this.renderChildren(ctx);\n      this.clearContext(ctx);\n    }\n\n    ctx.restore();\n  }\n\n  setContext(_) {// NO RENDER\n  }\n\n  applyEffects(ctx) {\n    // transform\n    var transform = Transform.fromElement(this.document, this);\n\n    if (transform) {\n      transform.apply(ctx);\n    } // clip\n\n\n    var clipPathStyleProp = this.getStyle('clip-path', false, true);\n\n    if (clipPathStyleProp.hasValue()) {\n      var clip = clipPathStyleProp.getDefinition();\n\n      if (clip) {\n        clip.apply(ctx);\n      }\n    }\n  }\n\n  clearContext(_) {// NO RENDER\n  }\n\n  renderChildren(ctx) {\n    this.children.forEach(child => {\n      child.render(ctx);\n    });\n  }\n\n  addChild(childNode) {\n    var child = childNode instanceof Element ? childNode : this.document.createElement(childNode);\n    child.parent = this;\n\n    if (!Element.ignoreChildTypes.includes(child.type)) {\n      this.children.push(child);\n    }\n  }\n\n  matchesSelector(selector) {\n    var _node$getAttribute;\n\n    var {\n      node\n    } = this;\n\n    if (typeof node.matches === 'function') {\n      return node.matches(selector);\n    }\n\n    var styleClasses = (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'class');\n\n    if (!styleClasses || styleClasses === '') {\n      return false;\n    }\n\n    return styleClasses.split(' ').some(styleClass => \".\".concat(styleClass) === selector);\n  }\n\n  addStylesFromStyleDefinition() {\n    var {\n      styles,\n      stylesSpecificity\n    } = this.document;\n\n    for (var selector in styles) {\n      if (!selector.startsWith('@') && this.matchesSelector(selector)) {\n        var style = styles[selector];\n        var specificity = stylesSpecificity[selector];\n\n        if (style) {\n          for (var name in style) {\n            var existingSpecificity = this.stylesSpecificity[name];\n\n            if (typeof existingSpecificity === 'undefined') {\n              existingSpecificity = '000';\n            }\n\n            if (specificity >= existingSpecificity) {\n              this.styles[name] = style[name];\n              this.stylesSpecificity[name] = specificity;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  removeStyles(element, ignoreStyles) {\n    var toRestore = ignoreStyles.reduce((toRestore, name) => {\n      var styleProp = element.getStyle(name);\n\n      if (!styleProp.hasValue()) {\n        return toRestore;\n      }\n\n      var value = styleProp.getString();\n      styleProp.setValue('');\n      return [...toRestore, [name, value]];\n    }, []);\n    return toRestore;\n  }\n\n  restoreStyles(element, styles) {\n    styles.forEach(_ref => {\n      var [name, value] = _ref;\n      element.getStyle(name, true).setValue(value);\n    });\n  }\n\n  isFirstChild() {\n    var _this$parent;\n\n    return ((_this$parent = this.parent) === null || _this$parent === void 0 ? void 0 : _this$parent.children.indexOf(this)) === 0;\n  }\n\n}\nElement.ignoreChildTypes = ['title'];\n\nclass UnknownElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n  }\n\n}\n\nfunction wrapFontFamily(fontFamily) {\n  var trimmed = fontFamily.trim();\n  return /^('|\")/.test(trimmed) ? trimmed : \"\\\"\".concat(trimmed, \"\\\"\");\n}\n\nfunction prepareFontFamily(fontFamily) {\n  return typeof process === 'undefined' ? fontFamily : fontFamily.trim().split(',').map(wrapFontFamily).join(',');\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-style\r\n * @param fontStyle\r\n * @returns CSS font style.\r\n */\n\n\nfunction prepareFontStyle(fontStyle) {\n  if (!fontStyle) {\n    return '';\n  }\n\n  var targetFontStyle = fontStyle.trim().toLowerCase();\n\n  switch (targetFontStyle) {\n    case 'normal':\n    case 'italic':\n    case 'oblique':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontStyle;\n\n    default:\n      if (/^oblique\\s+(-|)\\d+deg$/.test(targetFontStyle)) {\n        return targetFontStyle;\n      }\n\n      return '';\n  }\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight\r\n * @param fontWeight\r\n * @returns CSS font weight.\r\n */\n\n\nfunction prepareFontWeight(fontWeight) {\n  if (!fontWeight) {\n    return '';\n  }\n\n  var targetFontWeight = fontWeight.trim().toLowerCase();\n\n  switch (targetFontWeight) {\n    case 'normal':\n    case 'bold':\n    case 'lighter':\n    case 'bolder':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontWeight;\n\n    default:\n      if (/^[\\d.]+$/.test(targetFontWeight)) {\n        return targetFontWeight;\n      }\n\n      return '';\n  }\n}\n\nclass Font {\n  constructor(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit) {\n    var inheritFont = inherit ? typeof inherit === 'string' ? Font.parse(inherit) : inherit : {};\n    this.fontFamily = fontFamily || inheritFont.fontFamily;\n    this.fontSize = fontSize || inheritFont.fontSize;\n    this.fontStyle = fontStyle || inheritFont.fontStyle;\n    this.fontWeight = fontWeight || inheritFont.fontWeight;\n    this.fontVariant = fontVariant || inheritFont.fontVariant;\n  }\n\n  static parse() {\n    var font = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n    var inherit = arguments.length > 1 ? arguments[1] : undefined;\n    var fontStyle = '';\n    var fontVariant = '';\n    var fontWeight = '';\n    var fontSize = '';\n    var fontFamily = '';\n    var parts = compressSpaces(font).trim().split(' ');\n    var set = {\n      fontSize: false,\n      fontStyle: false,\n      fontWeight: false,\n      fontVariant: false\n    };\n    parts.forEach(part => {\n      switch (true) {\n        case !set.fontStyle && Font.styles.includes(part):\n          if (part !== 'inherit') {\n            fontStyle = part;\n          }\n\n          set.fontStyle = true;\n          break;\n\n        case !set.fontVariant && Font.variants.includes(part):\n          if (part !== 'inherit') {\n            fontVariant = part;\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          break;\n\n        case !set.fontWeight && Font.weights.includes(part):\n          if (part !== 'inherit') {\n            fontWeight = part;\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          break;\n\n        case !set.fontSize:\n          if (part !== 'inherit') {\n            [fontSize] = part.split('/');\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          set.fontSize = true;\n          break;\n\n        default:\n          if (part !== 'inherit') {\n            fontFamily += part;\n          }\n\n      }\n    });\n    return new Font(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit);\n  }\n\n  toString() {\n    return [prepareFontStyle(this.fontStyle), this.fontVariant, prepareFontWeight(this.fontWeight), this.fontSize, // Wrap fontFamily only on nodejs and only for canvas.ctx\n    prepareFontFamily(this.fontFamily)].join(' ').trim();\n  }\n\n}\nFont.styles = 'normal|italic|oblique|inherit';\nFont.variants = 'normal|small-caps|inherit';\nFont.weights = 'normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit';\n\nclass BoundingBox {\n  constructor() {\n    var x1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Number.NaN;\n    var y1 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.NaN;\n    var x2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.NaN;\n    var y2 = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : Number.NaN;\n    this.x1 = x1;\n    this.y1 = y1;\n    this.x2 = x2;\n    this.y2 = y2;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n\n  get x() {\n    return this.x1;\n  }\n\n  get y() {\n    return this.y1;\n  }\n\n  get width() {\n    return this.x2 - this.x1;\n  }\n\n  get height() {\n    return this.y2 - this.y1;\n  }\n\n  addPoint(x, y) {\n    if (typeof x !== 'undefined') {\n      if (isNaN(this.x1) || isNaN(this.x2)) {\n        this.x1 = x;\n        this.x2 = x;\n      }\n\n      if (x < this.x1) {\n        this.x1 = x;\n      }\n\n      if (x > this.x2) {\n        this.x2 = x;\n      }\n    }\n\n    if (typeof y !== 'undefined') {\n      if (isNaN(this.y1) || isNaN(this.y2)) {\n        this.y1 = y;\n        this.y2 = y;\n      }\n\n      if (y < this.y1) {\n        this.y1 = y;\n      }\n\n      if (y > this.y2) {\n        this.y2 = y;\n      }\n    }\n  }\n\n  addX(x) {\n    this.addPoint(x, null);\n  }\n\n  addY(y) {\n    this.addPoint(null, y);\n  }\n\n  addBoundingBox(boundingBox) {\n    if (!boundingBox) {\n      return;\n    }\n\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = boundingBox;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n\n  sumCubic(t, p0, p1, p2, p3) {\n    return Math.pow(1 - t, 3) * p0 + 3 * Math.pow(1 - t, 2) * t * p1 + 3 * (1 - t) * Math.pow(t, 2) * p2 + Math.pow(t, 3) * p3;\n  }\n\n  bezierCurveAdd(forX, p0, p1, p2, p3) {\n    var b = 6 * p0 - 12 * p1 + 6 * p2;\n    var a = -3 * p0 + 9 * p1 - 9 * p2 + 3 * p3;\n    var c = 3 * p1 - 3 * p0;\n\n    if (a === 0) {\n      if (b === 0) {\n        return;\n      }\n\n      var t = -c / b;\n\n      if (0 < t && t < 1) {\n        if (forX) {\n          this.addX(this.sumCubic(t, p0, p1, p2, p3));\n        } else {\n          this.addY(this.sumCubic(t, p0, p1, p2, p3));\n        }\n      }\n\n      return;\n    }\n\n    var b2ac = Math.pow(b, 2) - 4 * c * a;\n\n    if (b2ac < 0) {\n      return;\n    }\n\n    var t1 = (-b + Math.sqrt(b2ac)) / (2 * a);\n\n    if (0 < t1 && t1 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t1, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t1, p0, p1, p2, p3));\n      }\n    }\n\n    var t2 = (-b - Math.sqrt(b2ac)) / (2 * a);\n\n    if (0 < t2 && t2 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t2, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t2, p0, p1, p2, p3));\n      }\n    }\n  } // from http://blog.hackers-cafe.net/2009/06/how-to-calculate-bezier-curves-bounding.html\n\n\n  addBezierCurve(p0x, p0y, p1x, p1y, p2x, p2y, p3x, p3y) {\n    this.addPoint(p0x, p0y);\n    this.addPoint(p3x, p3y);\n    this.bezierCurveAdd(true, p0x, p1x, p2x, p3x);\n    this.bezierCurveAdd(false, p0y, p1y, p2y, p3y);\n  }\n\n  addQuadraticCurve(p0x, p0y, p1x, p1y, p2x, p2y) {\n    var cp1x = p0x + 2 / 3 * (p1x - p0x); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp1y = p0y + 2 / 3 * (p1y - p0y); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp2x = cp1x + 1 / 3 * (p2x - p0x); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    var cp2y = cp1y + 1 / 3 * (p2y - p0y); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    this.addBezierCurve(p0x, p0y, cp1x, cp2x, cp1y, cp2y, p2x, p2y);\n  }\n\n  isPointInBox(x, y) {\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = this;\n    return x1 <= x && x <= x2 && y1 <= y && y <= y2;\n  }\n\n}\n\nclass PathParser extends svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData {\n  constructor(path) {\n    super(path // Fix spaces after signs.\n    .replace(/([+\\-.])\\s+/gm, '$1') // Remove invalid part.\n    .replace(/[^MmZzLlHhVvCcSsQqTtAae\\d\\s.,+-].*/g, ''));\n    this.control = null;\n    this.start = null;\n    this.current = null;\n    this.command = null;\n    this.commands = this.commands;\n    this.i = -1;\n    this.previousCommand = null;\n    this.points = [];\n    this.angles = [];\n  }\n\n  reset() {\n    this.i = -1;\n    this.command = null;\n    this.previousCommand = null;\n    this.start = new Point(0, 0);\n    this.control = new Point(0, 0);\n    this.current = new Point(0, 0);\n    this.points = [];\n    this.angles = [];\n  }\n\n  isEnd() {\n    var {\n      i,\n      commands\n    } = this;\n    return i >= commands.length - 1;\n  }\n\n  next() {\n    var command = this.commands[++this.i];\n    this.previousCommand = this.command;\n    this.command = command;\n    return command;\n  }\n\n  getPoint() {\n    var xProp = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'x';\n    var yProp = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'y';\n    var point = new Point(this.command[xProp], this.command[yProp]);\n    return this.makeAbsolute(point);\n  }\n\n  getAsControlPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.control = point;\n    return point;\n  }\n\n  getAsCurrentPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.current = point;\n    return point;\n  }\n\n  getReflectedControlPoint() {\n    var previousCommand = this.previousCommand.type;\n\n    if (previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.CURVE_TO && previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.SMOOTH_CURVE_TO && previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.QUAD_TO && previousCommand !== svg_pathdata__WEBPACK_IMPORTED_MODULE_17__.SVGPathData.SMOOTH_QUAD_TO) {\n      return this.current;\n    } // reflect point\n\n\n    var {\n      current: {\n        x: cx,\n        y: cy\n      },\n      control: {\n        x: ox,\n        y: oy\n      }\n    } = this;\n    var point = new Point(2 * cx - ox, 2 * cy - oy);\n    return point;\n  }\n\n  makeAbsolute(point) {\n    if (this.command.relative) {\n      var {\n        x,\n        y\n      } = this.current;\n      point.x += x;\n      point.y += y;\n    }\n\n    return point;\n  }\n\n  addMarker(point, from, priorTo) {\n    var {\n      points,\n      angles\n    } = this; // if the last angle isn't filled in because we didn't have this point yet ...\n\n    if (priorTo && angles.length > 0 && !angles[angles.length - 1]) {\n      angles[angles.length - 1] = points[points.length - 1].angleTo(priorTo);\n    }\n\n    this.addMarkerAngle(point, from ? from.angleTo(point) : null);\n  }\n\n  addMarkerAngle(point, angle) {\n    this.points.push(point);\n    this.angles.push(angle);\n  }\n\n  getMarkerPoints() {\n    return this.points;\n  }\n\n  getMarkerAngles() {\n    var {\n      angles\n    } = this;\n    var len = angles.length;\n\n    for (var i = 0; i < len; i++) {\n      if (!angles[i]) {\n        for (var j = i + 1; j < len; j++) {\n          if (angles[j]) {\n            angles[i] = angles[j];\n            break;\n          }\n        }\n      }\n    }\n\n    return angles;\n  }\n\n}\n\nclass RenderedElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.modifiedEmSizeStack = false;\n  }\n\n  calculateOpacity() {\n    var opacity = 1.0; // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n\n    var element = this;\n\n    while (element) {\n      var opacityStyle = element.getStyle('opacity', false, true); // no ancestors on style call\n\n      if (opacityStyle.hasValue(true)) {\n        opacity *= opacityStyle.getNumber();\n      }\n\n      element = element.parent;\n    }\n\n    return opacity;\n  }\n\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n    if (!fromMeasure) {\n      // causes stack overflow when measuring text with gradients\n      // fill\n      var fillStyleProp = this.getStyle('fill');\n      var fillOpacityStyleProp = this.getStyle('fill-opacity');\n      var strokeStyleProp = this.getStyle('stroke');\n      var strokeOpacityProp = this.getStyle('stroke-opacity');\n\n      if (fillStyleProp.isUrlDefinition()) {\n        var fillStyle = fillStyleProp.getFillStyleDefinition(this, fillOpacityStyleProp);\n\n        if (fillStyle) {\n          ctx.fillStyle = fillStyle;\n        }\n      } else if (fillStyleProp.hasValue()) {\n        if (fillStyleProp.getString() === 'currentColor') {\n          fillStyleProp.setValue(this.getStyle('color').getColor());\n        }\n\n        var _fillStyle = fillStyleProp.getColor();\n\n        if (_fillStyle !== 'inherit') {\n          ctx.fillStyle = _fillStyle === 'none' ? 'rgba(0,0,0,0)' : _fillStyle;\n        }\n      }\n\n      if (fillOpacityStyleProp.hasValue()) {\n        var _fillStyle2 = new Property(this.document, 'fill', ctx.fillStyle).addOpacity(fillOpacityStyleProp).getColor();\n\n        ctx.fillStyle = _fillStyle2;\n      } // stroke\n\n\n      if (strokeStyleProp.isUrlDefinition()) {\n        var strokeStyle = strokeStyleProp.getFillStyleDefinition(this, strokeOpacityProp);\n\n        if (strokeStyle) {\n          ctx.strokeStyle = strokeStyle;\n        }\n      } else if (strokeStyleProp.hasValue()) {\n        if (strokeStyleProp.getString() === 'currentColor') {\n          strokeStyleProp.setValue(this.getStyle('color').getColor());\n        }\n\n        var _strokeStyle = strokeStyleProp.getString();\n\n        if (_strokeStyle !== 'inherit') {\n          ctx.strokeStyle = _strokeStyle === 'none' ? 'rgba(0,0,0,0)' : _strokeStyle;\n        }\n      }\n\n      if (strokeOpacityProp.hasValue()) {\n        var _strokeStyle2 = new Property(this.document, 'stroke', ctx.strokeStyle).addOpacity(strokeOpacityProp).getString();\n\n        ctx.strokeStyle = _strokeStyle2;\n      }\n\n      var strokeWidthStyleProp = this.getStyle('stroke-width');\n\n      if (strokeWidthStyleProp.hasValue()) {\n        var newLineWidth = strokeWidthStyleProp.getPixels();\n        ctx.lineWidth = !newLineWidth ? PSEUDO_ZERO // browsers don't respect 0 (or node-canvas? :-)\n        : newLineWidth;\n      }\n\n      var strokeLinecapStyleProp = this.getStyle('stroke-linecap');\n      var strokeLinejoinStyleProp = this.getStyle('stroke-linejoin');\n      var strokeMiterlimitProp = this.getStyle('stroke-miterlimit'); // NEED TEST\n      // const pointOrderStyleProp = this.getStyle('paint-order');\n\n      var strokeDasharrayStyleProp = this.getStyle('stroke-dasharray');\n      var strokeDashoffsetProp = this.getStyle('stroke-dashoffset');\n\n      if (strokeLinecapStyleProp.hasValue()) {\n        ctx.lineCap = strokeLinecapStyleProp.getString();\n      }\n\n      if (strokeLinejoinStyleProp.hasValue()) {\n        ctx.lineJoin = strokeLinejoinStyleProp.getString();\n      }\n\n      if (strokeMiterlimitProp.hasValue()) {\n        ctx.miterLimit = strokeMiterlimitProp.getNumber();\n      } // NEED TEST\n      // if (pointOrderStyleProp.hasValue()) {\n      // \t// ?\n      // \tctx.paintOrder = pointOrderStyleProp.getValue();\n      // }\n\n\n      if (strokeDasharrayStyleProp.hasValue() && strokeDasharrayStyleProp.getString() !== 'none') {\n        var gaps = toNumbers(strokeDasharrayStyleProp.getString());\n\n        if (typeof ctx.setLineDash !== 'undefined') {\n          ctx.setLineDash(gaps);\n        } else // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDash !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDash = gaps;\n          } else // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDash !== 'undefined' && !(gaps.length === 1 && gaps[0] === 0)) {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDash = gaps;\n            }\n\n        var offset = strokeDashoffsetProp.getPixels();\n\n        if (typeof ctx.lineDashOffset !== 'undefined') {\n          ctx.lineDashOffset = offset;\n        } else // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDashOffset !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDashOffset = offset;\n          } else // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDashOffset !== 'undefined') {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDashOffset = offset;\n            }\n      }\n    } // font\n\n\n    this.modifiedEmSizeStack = false;\n\n    if (typeof ctx.font !== 'undefined') {\n      var fontStyleProp = this.getStyle('font');\n      var fontStyleStyleProp = this.getStyle('font-style');\n      var fontVariantStyleProp = this.getStyle('font-variant');\n      var fontWeightStyleProp = this.getStyle('font-weight');\n      var fontSizeStyleProp = this.getStyle('font-size');\n      var fontFamilyStyleProp = this.getStyle('font-family');\n      var font = new Font(fontStyleStyleProp.getString(), fontVariantStyleProp.getString(), fontWeightStyleProp.getString(), fontSizeStyleProp.hasValue() ? \"\".concat(fontSizeStyleProp.getPixels(true), \"px\") : '', fontFamilyStyleProp.getString(), Font.parse(fontStyleProp.getString(), ctx.font));\n      fontStyleStyleProp.setValue(font.fontStyle);\n      fontVariantStyleProp.setValue(font.fontVariant);\n      fontWeightStyleProp.setValue(font.fontWeight);\n      fontSizeStyleProp.setValue(font.fontSize);\n      fontFamilyStyleProp.setValue(font.fontFamily);\n      ctx.font = font.toString();\n\n      if (fontSizeStyleProp.isPixels()) {\n        this.document.emSize = fontSizeStyleProp.getPixels();\n        this.modifiedEmSizeStack = true;\n      }\n    }\n\n    if (!fromMeasure) {\n      // effects\n      this.applyEffects(ctx); // opacity\n\n      ctx.globalAlpha = this.calculateOpacity();\n    }\n  }\n\n  clearContext(ctx) {\n    super.clearContext(ctx);\n\n    if (this.modifiedEmSizeStack) {\n      this.document.popEmSize();\n    }\n  }\n\n}\n\nclass PathElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'path';\n    this.pathParser = null;\n    this.pathParser = new PathParser(this.getAttribute('d').getString());\n  }\n\n  path(ctx) {\n    var {\n      pathParser\n    } = this;\n    var boundingBox = new BoundingBox();\n    pathParser.reset();\n\n    if (ctx) {\n      ctx.beginPath();\n    }\n\n    while (!pathParser.isEnd()) {\n      switch (pathParser.next().type) {\n        case PathParser.MOVE_TO:\n          this.pathM(ctx, boundingBox);\n          break;\n\n        case PathParser.LINE_TO:\n          this.pathL(ctx, boundingBox);\n          break;\n\n        case PathParser.HORIZ_LINE_TO:\n          this.pathH(ctx, boundingBox);\n          break;\n\n        case PathParser.VERT_LINE_TO:\n          this.pathV(ctx, boundingBox);\n          break;\n\n        case PathParser.CURVE_TO:\n          this.pathC(ctx, boundingBox);\n          break;\n\n        case PathParser.SMOOTH_CURVE_TO:\n          this.pathS(ctx, boundingBox);\n          break;\n\n        case PathParser.QUAD_TO:\n          this.pathQ(ctx, boundingBox);\n          break;\n\n        case PathParser.SMOOTH_QUAD_TO:\n          this.pathT(ctx, boundingBox);\n          break;\n\n        case PathParser.ARC:\n          this.pathA(ctx, boundingBox);\n          break;\n\n        case PathParser.CLOSE_PATH:\n          this.pathZ(ctx, boundingBox);\n          break;\n      }\n    }\n\n    return boundingBox;\n  }\n\n  getBoundingBox(_) {\n    return this.path();\n  }\n\n  getMarkers() {\n    var {\n      pathParser\n    } = this;\n    var points = pathParser.getMarkerPoints();\n    var angles = pathParser.getMarkerAngles();\n    var markers = points.map((point, i) => [point, angles[i]]);\n    return markers;\n  }\n\n  renderChildren(ctx) {\n    this.path(ctx);\n    this.document.screen.mouse.checkPath(this, ctx);\n    var fillRuleStyleProp = this.getStyle('fill-rule');\n\n    if (ctx.fillStyle !== '') {\n      if (fillRuleStyleProp.getString('inherit') !== 'inherit') {\n        ctx.fill(fillRuleStyleProp.getString());\n      } else {\n        ctx.fill();\n      }\n    }\n\n    if (ctx.strokeStyle !== '') {\n      if (this.getAttribute('vector-effect').getString() === 'non-scaling-stroke') {\n        ctx.save();\n        ctx.setTransform(1, 0, 0, 1, 0, 0);\n        ctx.stroke();\n        ctx.restore();\n      } else {\n        ctx.stroke();\n      }\n    }\n\n    var markers = this.getMarkers();\n\n    if (markers) {\n      var markersLastIndex = markers.length - 1;\n      var markerStartStyleProp = this.getStyle('marker-start');\n      var markerMidStyleProp = this.getStyle('marker-mid');\n      var markerEndStyleProp = this.getStyle('marker-end');\n\n      if (markerStartStyleProp.isUrlDefinition()) {\n        var marker = markerStartStyleProp.getDefinition();\n        var [point, angle] = markers[0];\n        marker.render(ctx, point, angle);\n      }\n\n      if (markerMidStyleProp.isUrlDefinition()) {\n        var _marker = markerMidStyleProp.getDefinition();\n\n        for (var i = 1; i < markersLastIndex; i++) {\n          var [_point, _angle] = markers[i];\n\n          _marker.render(ctx, _point, _angle);\n        }\n      }\n\n      if (markerEndStyleProp.isUrlDefinition()) {\n        var _marker2 = markerEndStyleProp.getDefinition();\n\n        var [_point2, _angle2] = markers[markersLastIndex];\n\n        _marker2.render(ctx, _point2, _angle2);\n      }\n    }\n  }\n\n  static pathM(pathParser) {\n    var point = pathParser.getAsCurrentPoint();\n    pathParser.start = pathParser.current;\n    return {\n      point\n    };\n  }\n\n  pathM(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      point\n    } = PathElement.pathM(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.moveTo(x, y);\n    }\n  }\n\n  static pathL(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point\n    };\n  }\n\n  pathL(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathL(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathH(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point((command.relative ? current.x : 0) + command.x, current.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n\n  pathH(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathH(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathV(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point(current.x, (command.relative ? current.y : 0) + command.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n\n  pathV(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathV(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathC(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getPoint('x1', 'y1');\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathC(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathS(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getReflectedControlPoint();\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathS(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathQ(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getAsControlPoint('x1', 'y1');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathQ(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathT(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getReflectedControlPoint();\n    pathParser.control = controlPoint;\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathT(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathA(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var {\n      rX,\n      rY,\n      xRot,\n      lArcFlag,\n      sweepFlag\n    } = command;\n    var xAxisRotation = xRot * (Math.PI / 180.0);\n    var currentPoint = pathParser.getAsCurrentPoint(); // Conversion from endpoint to center parameterization\n    // http://www.w3.org/TR/SVG11/implnote.html#ArcImplementationNotes\n    // x1', y1'\n\n    var currp = new Point(Math.cos(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.sin(xAxisRotation) * (current.y - currentPoint.y) / 2.0, -Math.sin(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * (current.y - currentPoint.y) / 2.0); // adjust radii\n\n    var l = Math.pow(currp.x, 2) / Math.pow(rX, 2) + Math.pow(currp.y, 2) / Math.pow(rY, 2);\n\n    if (l > 1) {\n      rX *= Math.sqrt(l);\n      rY *= Math.sqrt(l);\n    } // cx', cy'\n\n\n    var s = (lArcFlag === sweepFlag ? -1 : 1) * Math.sqrt((Math.pow(rX, 2) * Math.pow(rY, 2) - Math.pow(rX, 2) * Math.pow(currp.y, 2) - Math.pow(rY, 2) * Math.pow(currp.x, 2)) / (Math.pow(rX, 2) * Math.pow(currp.y, 2) + Math.pow(rY, 2) * Math.pow(currp.x, 2)));\n\n    if (isNaN(s)) {\n      s = 0;\n    }\n\n    var cpp = new Point(s * rX * currp.y / rY, s * -rY * currp.x / rX); // cx, cy\n\n    var centp = new Point((current.x + currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * cpp.x - Math.sin(xAxisRotation) * cpp.y, (current.y + currentPoint.y) / 2.0 + Math.sin(xAxisRotation) * cpp.x + Math.cos(xAxisRotation) * cpp.y); // initial angle\n\n    var a1 = vectorsAngle([1, 0], [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY]); // θ1\n    // angle delta\n\n    var u = [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY];\n    var v = [(-currp.x - cpp.x) / rX, (-currp.y - cpp.y) / rY];\n    var ad = vectorsAngle(u, v); // Δθ\n\n    if (vectorsRatio(u, v) <= -1) {\n      ad = Math.PI;\n    }\n\n    if (vectorsRatio(u, v) >= 1) {\n      ad = 0;\n    }\n\n    return {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    };\n  }\n\n  pathA(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser); // for markers\n\n    var dir = 1 - sweepFlag ? 1.0 : -1.0;\n    var ah = a1 + dir * (ad / 2.0);\n    var halfWay = new Point(centp.x + rX * Math.cos(ah), centp.y + rY * Math.sin(ah));\n    pathParser.addMarkerAngle(halfWay, ah - dir * Math.PI / 2);\n    pathParser.addMarkerAngle(currentPoint, ah - dir * Math.PI);\n    boundingBox.addPoint(currentPoint.x, currentPoint.y); // TODO: this is too naive, make it better\n\n    if (ctx && !isNaN(a1) && !isNaN(ad)) {\n      var r = rX > rY ? rX : rY;\n      var sx = rX > rY ? 1 : rX / rY;\n      var sy = rX > rY ? rY / rX : 1;\n      ctx.translate(centp.x, centp.y);\n      ctx.rotate(xAxisRotation);\n      ctx.scale(sx, sy);\n      ctx.arc(0, 0, r, a1, a1 + ad, Boolean(1 - sweepFlag));\n      ctx.scale(1 / sx, 1 / sy);\n      ctx.rotate(-xAxisRotation);\n      ctx.translate(-centp.x, -centp.y);\n    }\n  }\n\n  static pathZ(pathParser) {\n    pathParser.current = pathParser.start;\n  }\n\n  pathZ(ctx, boundingBox) {\n    PathElement.pathZ(this.pathParser);\n\n    if (ctx) {\n      // only close path if it is not a straight line\n      if (boundingBox.x1 !== boundingBox.x2 && boundingBox.y1 !== boundingBox.y2) {\n        ctx.closePath();\n      }\n    }\n  }\n\n}\n\nclass GlyphElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'glyph';\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    this.unicode = this.getAttribute('unicode').getString();\n    this.arabicForm = this.getAttribute('arabic-form').getString();\n  }\n\n}\n\nclass TextElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TextElement ? true : captureTextNodes);\n    this.type = 'text';\n    this.x = 0;\n    this.y = 0;\n    this.measureCache = -1;\n  }\n\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.setContext(ctx, fromMeasure);\n    var textBaseline = this.getStyle('dominant-baseline').getTextBaseline() || this.getStyle('alignment-baseline').getTextBaseline();\n\n    if (textBaseline) {\n      ctx.textBaseline = textBaseline;\n    }\n  }\n\n  initializeCoordinates() {\n    this.x = 0;\n    this.y = 0;\n    this.leafTexts = [];\n    this.textChunkStart = 0;\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n  }\n\n  getBoundingBox(ctx) {\n    if (this.type !== 'text') {\n      return this.getTElementBoundingBox(ctx);\n    } // first, calculate child positions\n\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx);\n    var boundingBox = null; // then calculate bounding box\n\n    this.children.forEach((_, i) => {\n      var childBoundingBox = this.getChildBoundingBox(ctx, this, this, i);\n\n      if (!boundingBox) {\n        boundingBox = childBoundingBox;\n      } else {\n        boundingBox.addBoundingBox(childBoundingBox);\n      }\n    });\n    return boundingBox;\n  }\n\n  getFontSize() {\n    var {\n      document,\n      parent\n    } = this;\n    var inheritFontSize = Font.parse(document.ctx.font).fontSize;\n    var fontSize = parent.getStyle('font-size').getNumber(inheritFontSize);\n    return fontSize;\n  }\n\n  getTElementBoundingBox(ctx) {\n    var fontSize = this.getFontSize();\n    return new BoundingBox(this.x, this.y - fontSize, this.x + this.measureText(ctx), this.y);\n  }\n\n  getGlyph(font, text, i) {\n    var char = text[i];\n    var glyph = null;\n\n    if (font.isArabic) {\n      var len = text.length;\n      var prevChar = text[i - 1];\n      var nextChar = text[i + 1];\n      var arabicForm = 'isolated';\n\n      if ((i === 0 || prevChar === ' ') && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'terminal';\n      }\n\n      if (i > 0 && prevChar !== ' ' && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'medial';\n      }\n\n      if (i > 0 && prevChar !== ' ' && (i === len - 1 || nextChar === ' ')) {\n        arabicForm = 'initial';\n      }\n\n      if (typeof font.glyphs[char] !== 'undefined') {\n        // NEED TEST\n        var maybeGlyph = font.glyphs[char];\n        glyph = maybeGlyph instanceof GlyphElement ? maybeGlyph : maybeGlyph[arabicForm];\n      }\n    } else {\n      glyph = font.glyphs[char];\n    }\n\n    if (!glyph) {\n      glyph = font.missingGlyph;\n    }\n\n    return glyph;\n  }\n\n  getText() {\n    return '';\n  }\n\n  getTextFromNode(node) {\n    var textNode = node || this.node;\n    var childNodes = Array.from(textNode.parentNode.childNodes);\n    var index = childNodes.indexOf(textNode);\n    var lastIndex = childNodes.length - 1;\n    var text = compressSpaces( // textNode.value\n    // || textNode.text\n    textNode.textContent || '');\n\n    if (index === 0) {\n      text = trimLeft(text);\n    }\n\n    if (index === lastIndex) {\n      text = trimRight(text);\n    }\n\n    return text;\n  }\n\n  renderChildren(ctx) {\n    if (this.type !== 'text') {\n      this.renderTElementChildren(ctx);\n      return;\n    } // first, calculate child positions\n\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx); // then render\n\n    this.children.forEach((_, i) => {\n      this.renderChild(ctx, this, this, i);\n    });\n    var {\n      mouse\n    } = this.document.screen; // Do not calc bounding box if mouse is not working.\n\n    if (mouse.isWorking()) {\n      mouse.checkBoundingBox(this, this.getBoundingBox(ctx));\n    }\n  }\n\n  renderTElementChildren(ctx) {\n    var {\n      document,\n      parent\n    } = this;\n    var renderText = this.getText();\n    var customFont = parent.getStyle('font-family').getDefinition();\n\n    if (customFont) {\n      var {\n        unitsPerEm\n      } = customFont.fontFace;\n      var ctxFont = Font.parse(document.ctx.font);\n      var fontSize = parent.getStyle('font-size').getNumber(ctxFont.fontSize);\n      var fontStyle = parent.getStyle('font-style').getString(ctxFont.fontStyle);\n      var scale = fontSize / unitsPerEm;\n      var text = customFont.isRTL ? renderText.split('').reverse().join('') : renderText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        ctx.translate(this.x, this.y);\n        ctx.scale(scale, -scale);\n        var lw = ctx.lineWidth;\n        ctx.lineWidth = ctx.lineWidth * unitsPerEm / fontSize;\n\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, .4, 1, 0, 0);\n        }\n\n        glyph.render(ctx);\n\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, -.4, 1, 0, 0);\n        }\n\n        ctx.lineWidth = lw;\n        ctx.scale(1 / scale, -1 / scale);\n        ctx.translate(-this.x, -this.y);\n        this.x += fontSize * (glyph.horizAdvX || customFont.horizAdvX) / unitsPerEm;\n\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          this.x += dx[i];\n        }\n      }\n\n      return;\n    }\n\n    var {\n      x,\n      y\n    } = this; // NEED TEST\n    // if (ctx.paintOrder === 'stroke') {\n    // \tif (ctx.strokeStyle) {\n    // \t\tctx.strokeText(renderText, x, y);\n    // \t}\n    // \tif (ctx.fillStyle) {\n    // \t\tctx.fillText(renderText, x, y);\n    // \t}\n    // } else {\n\n    if (ctx.fillStyle) {\n      ctx.fillText(renderText, x, y);\n    }\n\n    if (ctx.strokeStyle) {\n      ctx.strokeText(renderText, x, y);\n    } // }\n\n  }\n\n  applyAnchoring() {\n    if (this.textChunkStart >= this.leafTexts.length) {\n      return;\n    } // This is basically the \"Apply anchoring\" part of https://www.w3.org/TR/SVG2/text.html#TextLayoutAlgorithm.\n    // The difference is that we apply the anchoring as soon as a chunk is finished. This saves some extra looping.\n    // Vertical text is not supported.\n\n\n    var firstElement = this.leafTexts[this.textChunkStart];\n    var textAnchor = firstElement.getStyle('text-anchor').getString('start');\n    var isRTL = false; // we treat RTL like LTR\n\n    var shift = 0;\n\n    if (textAnchor === 'start' && !isRTL || textAnchor === 'end' && isRTL) {\n      shift = firstElement.x - this.minX;\n    } else if (textAnchor === 'end' && !isRTL || textAnchor === 'start' && isRTL) {\n      shift = firstElement.x - this.maxX;\n    } else {\n      shift = firstElement.x - (this.minX + this.maxX) / 2;\n    }\n\n    for (var i = this.textChunkStart; i < this.leafTexts.length; i++) {\n      this.leafTexts[i].x += shift;\n    } // start new chunk\n\n\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n    this.textChunkStart = this.leafTexts.length;\n  }\n\n  adjustChildCoordinatesRecursive(ctx) {\n    this.children.forEach((_, i) => {\n      this.adjustChildCoordinatesRecursiveCore(ctx, this, this, i);\n    });\n    this.applyAnchoring();\n  }\n\n  adjustChildCoordinatesRecursiveCore(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n\n    if (child.children.length > 0) {\n      child.children.forEach((_, i) => {\n        textParent.adjustChildCoordinatesRecursiveCore(ctx, textParent, child, i);\n      });\n    } else {\n      // only leafs are relevant\n      this.adjustChildCoordinates(ctx, textParent, parent, i);\n    }\n  }\n\n  adjustChildCoordinates(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n\n    if (typeof child.measureText !== 'function') {\n      return child;\n    }\n\n    ctx.save();\n    child.setContext(ctx, true);\n    var xAttr = child.getAttribute('x');\n    var yAttr = child.getAttribute('y');\n    var dxAttr = child.getAttribute('dx');\n    var dyAttr = child.getAttribute('dy');\n    var customFont = child.getStyle('font-family').getDefinition();\n    var isRTL = Boolean(customFont) && customFont.isRTL;\n\n    if (i === 0) {\n      // First children inherit attributes from parent(s). Positional attributes\n      // are only inherited from a parent to it's first child.\n      if (!xAttr.hasValue()) {\n        xAttr.setValue(child.getInheritedAttribute('x'));\n      }\n\n      if (!yAttr.hasValue()) {\n        yAttr.setValue(child.getInheritedAttribute('y'));\n      }\n\n      if (!dxAttr.hasValue()) {\n        dxAttr.setValue(child.getInheritedAttribute('dx'));\n      }\n\n      if (!dyAttr.hasValue()) {\n        dyAttr.setValue(child.getInheritedAttribute('dy'));\n      }\n    }\n\n    var width = child.measureText(ctx);\n\n    if (isRTL) {\n      textParent.x -= width;\n    }\n\n    if (xAttr.hasValue()) {\n      // an \"x\" attribute marks the start of a new chunk\n      textParent.applyAnchoring();\n      child.x = xAttr.getPixels('x');\n\n      if (dxAttr.hasValue()) {\n        child.x += dxAttr.getPixels('x');\n      }\n    } else {\n      if (dxAttr.hasValue()) {\n        textParent.x += dxAttr.getPixels('x');\n      }\n\n      child.x = textParent.x;\n    }\n\n    textParent.x = child.x;\n\n    if (!isRTL) {\n      textParent.x += width;\n    }\n\n    if (yAttr.hasValue()) {\n      child.y = yAttr.getPixels('y');\n\n      if (dyAttr.hasValue()) {\n        child.y += dyAttr.getPixels('y');\n      }\n    } else {\n      if (dyAttr.hasValue()) {\n        textParent.y += dyAttr.getPixels('y');\n      }\n\n      child.y = textParent.y;\n    }\n\n    textParent.y = child.y; // update the current chunk and it's bounds\n\n    textParent.leafTexts.push(child);\n    textParent.minX = Math.min(textParent.minX, child.x, child.x + width);\n    textParent.maxX = Math.max(textParent.maxX, child.x, child.x + width);\n    child.clearContext(ctx);\n    ctx.restore();\n    return child;\n  }\n\n  getChildBoundingBox(ctx, textParent, parent, i) {\n    var child = parent.children[i]; // not a text node?\n\n    if (typeof child.getBoundingBox !== 'function') {\n      return null;\n    }\n\n    var boundingBox = child.getBoundingBox(ctx);\n\n    if (!boundingBox) {\n      return null;\n    }\n\n    child.children.forEach((_, i) => {\n      var childBoundingBox = textParent.getChildBoundingBox(ctx, textParent, child, i);\n      boundingBox.addBoundingBox(childBoundingBox);\n    });\n    return boundingBox;\n  }\n\n  renderChild(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n    child.render(ctx);\n    child.children.forEach((_, i) => {\n      textParent.renderChild(ctx, textParent, child, i);\n    });\n  }\n\n  measureText(ctx) {\n    var {\n      measureCache\n    } = this;\n\n    if (~measureCache) {\n      return measureCache;\n    }\n\n    var renderText = this.getText();\n    var measure = this.measureTargetText(ctx, renderText);\n    this.measureCache = measure;\n    return measure;\n  }\n\n  measureTargetText(ctx, targetText) {\n    if (!targetText.length) {\n      return 0;\n    }\n\n    var {\n      parent\n    } = this;\n    var customFont = parent.getStyle('font-family').getDefinition();\n\n    if (customFont) {\n      var fontSize = this.getFontSize();\n      var text = customFont.isRTL ? targetText.split('').reverse().join('') : targetText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n      var _measure = 0;\n\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        _measure += (glyph.horizAdvX || customFont.horizAdvX) * fontSize / customFont.fontFace.unitsPerEm;\n\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          _measure += dx[i];\n        }\n      }\n\n      return _measure;\n    }\n\n    if (!ctx.measureText) {\n      return targetText.length * 10;\n    }\n\n    ctx.save();\n    this.setContext(ctx, true);\n    var {\n      width: measure\n    } = ctx.measureText(targetText);\n    this.clearContext(ctx);\n    ctx.restore();\n    return measure;\n  }\n  /**\r\n   * Inherits positional attributes from {@link TextElement} parent(s). Attributes\r\n   * are only inherited from a parent to its first child.\r\n   * @param name - The attribute name.\r\n   * @returns The attribute value or null.\r\n   */\n\n\n  getInheritedAttribute(name) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias,consistent-this\n    var current = this;\n\n    while (current instanceof TextElement && current.isFirstChild()) {\n      var parentAttr = current.parent.getAttribute(name);\n\n      if (parentAttr.hasValue(true)) {\n        return parentAttr.getValue('0');\n      }\n\n      current = current.parent;\n    }\n\n    return null;\n  }\n\n}\n\nclass TSpanElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TSpanElement ? true : captureTextNodes);\n    this.type = 'tspan'; // if this node has children, then they own the text\n\n    this.text = this.children.length > 0 ? '' : this.getTextFromNode();\n  }\n\n  getText() {\n    return this.text;\n  }\n\n}\n\nclass TextNode extends TSpanElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'textNode';\n  }\n\n}\n\nclass SVGElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'svg';\n    this.root = false;\n  }\n\n  setContext(ctx) {\n    var _this$node$parentNode;\n\n    var {\n      document\n    } = this;\n    var {\n      screen,\n      window\n    } = document;\n    var canvas = ctx.canvas;\n    screen.setDefaults(ctx);\n\n    if (canvas.style && typeof ctx.font !== 'undefined' && window && typeof window.getComputedStyle !== 'undefined') {\n      ctx.font = window.getComputedStyle(canvas).getPropertyValue('font');\n      var fontSizeProp = new Property(document, 'fontSize', Font.parse(ctx.font).fontSize);\n\n      if (fontSizeProp.hasValue()) {\n        document.rootEmSize = fontSizeProp.getPixels('y');\n        document.emSize = document.rootEmSize;\n      }\n    } // create new view port\n\n\n    if (!this.getAttribute('x').hasValue()) {\n      this.getAttribute('x', true).setValue(0);\n    }\n\n    if (!this.getAttribute('y').hasValue()) {\n      this.getAttribute('y', true).setValue(0);\n    }\n\n    var {\n      width,\n      height\n    } = screen.viewPort;\n\n    if (!this.getStyle('width').hasValue()) {\n      this.getStyle('width', true).setValue('100%');\n    }\n\n    if (!this.getStyle('height').hasValue()) {\n      this.getStyle('height', true).setValue('100%');\n    }\n\n    if (!this.getStyle('color').hasValue()) {\n      this.getStyle('color', true).setValue('black');\n    }\n\n    var refXAttr = this.getAttribute('refX');\n    var refYAttr = this.getAttribute('refY');\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var viewBox = viewBoxAttr.hasValue() ? toNumbers(viewBoxAttr.getString()) : null;\n    var clip = !this.root && this.getStyle('overflow').getValue('hidden') !== 'visible';\n    var minX = 0;\n    var minY = 0;\n    var clipX = 0;\n    var clipY = 0;\n\n    if (viewBox) {\n      minX = viewBox[0];\n      minY = viewBox[1];\n    }\n\n    if (!this.root) {\n      width = this.getStyle('width').getPixels('x');\n      height = this.getStyle('height').getPixels('y');\n\n      if (this.type === 'marker') {\n        clipX = minX;\n        clipY = minY;\n        minX = 0;\n        minY = 0;\n      }\n    }\n\n    screen.viewPort.setCurrent(width, height); // Default value of transform-origin is center only for root SVG elements\n    // https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/transform-origin\n\n    if (this.node // is not temporary SVGElement\n    && (!this.parent || ((_this$node$parentNode = this.node.parentNode) === null || _this$node$parentNode === void 0 ? void 0 : _this$node$parentNode.nodeName) === 'foreignObject') && this.getStyle('transform', false, true).hasValue() && !this.getStyle('transform-origin', false, true).hasValue()) {\n      this.getStyle('transform-origin', true, true).setValue('50% 50%');\n    }\n\n    super.setContext(ctx);\n    ctx.translate(this.getAttribute('x').getPixels('x'), this.getAttribute('y').getPixels('y'));\n\n    if (viewBox) {\n      width = viewBox[2];\n      height = viewBox[3];\n    }\n\n    document.setViewBox({\n      ctx,\n      aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n      width: screen.viewPort.width,\n      desiredWidth: width,\n      height: screen.viewPort.height,\n      desiredHeight: height,\n      minX,\n      minY,\n      refX: refXAttr.getValue(),\n      refY: refYAttr.getValue(),\n      clip,\n      clipX,\n      clipY\n    });\n\n    if (viewBox) {\n      screen.viewPort.removeCurrent();\n      screen.viewPort.setCurrent(width, height);\n    }\n  }\n\n  clearContext(ctx) {\n    super.clearContext(ctx);\n    this.document.screen.viewPort.removeCurrent();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var widthAttr = this.getAttribute('width', true);\n    var heightAttr = this.getAttribute('height', true);\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var styleAttr = this.getAttribute('style');\n    var originWidth = widthAttr.getNumber(0);\n    var originHeight = heightAttr.getNumber(0);\n\n    if (preserveAspectRatio) {\n      if (typeof preserveAspectRatio === 'string') {\n        this.getAttribute('preserveAspectRatio', true).setValue(preserveAspectRatio);\n      } else {\n        var preserveAspectRatioAttr = this.getAttribute('preserveAspectRatio');\n\n        if (preserveAspectRatioAttr.hasValue()) {\n          preserveAspectRatioAttr.setValue(preserveAspectRatioAttr.getString().replace(/^\\s*(\\S.*\\S)\\s*$/, '$1'));\n        }\n      }\n    }\n\n    widthAttr.setValue(width);\n    heightAttr.setValue(height);\n\n    if (!viewBoxAttr.hasValue()) {\n      viewBoxAttr.setValue(\"0 0 \".concat(originWidth || width, \" \").concat(originHeight || height));\n    }\n\n    if (styleAttr.hasValue()) {\n      var widthStyle = this.getStyle('width');\n      var heightStyle = this.getStyle('height');\n\n      if (widthStyle.hasValue()) {\n        widthStyle.setValue(\"\".concat(width, \"px\"));\n      }\n\n      if (heightStyle.hasValue()) {\n        heightStyle.setValue(\"\".concat(height, \"px\"));\n      }\n    }\n  }\n\n}\n\nclass RectElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'rect';\n  }\n\n  path(ctx) {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width', false, true).getPixels('x');\n    var height = this.getStyle('height', false, true).getPixels('y');\n    var rxAttr = this.getAttribute('rx');\n    var ryAttr = this.getAttribute('ry');\n    var rx = rxAttr.getPixels('x');\n    var ry = ryAttr.getPixels('y');\n\n    if (rxAttr.hasValue() && !ryAttr.hasValue()) {\n      ry = rx;\n    }\n\n    if (ryAttr.hasValue() && !rxAttr.hasValue()) {\n      rx = ry;\n    }\n\n    rx = Math.min(rx, width / 2.0);\n    ry = Math.min(ry, height / 2.0);\n\n    if (ctx) {\n      var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n      ctx.beginPath(); // always start the path so we don't fill prior paths\n\n      if (height > 0 && width > 0) {\n        ctx.moveTo(x + rx, y);\n        ctx.lineTo(x + width - rx, y);\n        ctx.bezierCurveTo(x + width - rx + KAPPA * rx, y, x + width, y + ry - KAPPA * ry, x + width, y + ry);\n        ctx.lineTo(x + width, y + height - ry);\n        ctx.bezierCurveTo(x + width, y + height - ry + KAPPA * ry, x + width - rx + KAPPA * rx, y + height, x + width - rx, y + height);\n        ctx.lineTo(x + rx, y + height);\n        ctx.bezierCurveTo(x + rx - KAPPA * rx, y + height, x, y + height - ry + KAPPA * ry, x, y + height - ry);\n        ctx.lineTo(x, y + ry);\n        ctx.bezierCurveTo(x, y + ry - KAPPA * ry, x + rx - KAPPA * rx, y, x + rx, y);\n        ctx.closePath();\n      }\n    }\n\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass CircleElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'circle';\n  }\n\n  path(ctx) {\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n    var r = this.getAttribute('r').getPixels();\n\n    if (ctx && r > 0) {\n      ctx.beginPath();\n      ctx.arc(cx, cy, r, 0, Math.PI * 2, false);\n      ctx.closePath();\n    }\n\n    return new BoundingBox(cx - r, cy - r, cx + r, cy + r);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass EllipseElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'ellipse';\n  }\n\n  path(ctx) {\n    var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n    var rx = this.getAttribute('rx').getPixels('x');\n    var ry = this.getAttribute('ry').getPixels('y');\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n\n    if (ctx && rx > 0 && ry > 0) {\n      ctx.beginPath();\n      ctx.moveTo(cx + rx, cy);\n      ctx.bezierCurveTo(cx + rx, cy + KAPPA * ry, cx + KAPPA * rx, cy + ry, cx, cy + ry);\n      ctx.bezierCurveTo(cx - KAPPA * rx, cy + ry, cx - rx, cy + KAPPA * ry, cx - rx, cy);\n      ctx.bezierCurveTo(cx - rx, cy - KAPPA * ry, cx - KAPPA * rx, cy - ry, cx, cy - ry);\n      ctx.bezierCurveTo(cx + KAPPA * rx, cy - ry, cx + rx, cy - KAPPA * ry, cx + rx, cy);\n      ctx.closePath();\n    }\n\n    return new BoundingBox(cx - rx, cy - ry, cx + rx, cy + ry);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass LineElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'line';\n  }\n\n  getPoints() {\n    return [new Point(this.getAttribute('x1').getPixels('x'), this.getAttribute('y1').getPixels('y')), new Point(this.getAttribute('x2').getPixels('x'), this.getAttribute('y2').getPixels('y'))];\n  }\n\n  path(ctx) {\n    var [{\n      x: x0,\n      y: y0\n    }, {\n      x: x1,\n      y: y1\n    }] = this.getPoints();\n\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n      ctx.lineTo(x1, y1);\n    }\n\n    return new BoundingBox(x0, y0, x1, y1);\n  }\n\n  getMarkers() {\n    var [p0, p1] = this.getPoints();\n    var a = p0.angleTo(p1);\n    return [[p0, a], [p1, a]];\n  }\n\n}\n\nclass PolylineElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'polyline';\n    this.points = [];\n    this.points = Point.parsePath(this.getAttribute('points').getString());\n  }\n\n  path(ctx) {\n    var {\n      points\n    } = this;\n    var [{\n      x: x0,\n      y: y0\n    }] = points;\n    var boundingBox = new BoundingBox(x0, y0);\n\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n    }\n\n    points.forEach(_ref => {\n      var {\n        x,\n        y\n      } = _ref;\n      boundingBox.addPoint(x, y);\n\n      if (ctx) {\n        ctx.lineTo(x, y);\n      }\n    });\n    return boundingBox;\n  }\n\n  getMarkers() {\n    var {\n      points\n    } = this;\n    var lastIndex = points.length - 1;\n    var markers = [];\n    points.forEach((point, i) => {\n      if (i === lastIndex) {\n        return;\n      }\n\n      markers.push([point, point.angleTo(points[i + 1])]);\n    });\n\n    if (markers.length > 0) {\n      markers.push([points[points.length - 1], markers[markers.length - 1][1]]);\n    }\n\n    return markers;\n  }\n\n}\n\nclass PolygonElement extends PolylineElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'polygon';\n  }\n\n  path(ctx) {\n    var boundingBox = super.path(ctx);\n    var [{\n      x,\n      y\n    }] = this.points;\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n      ctx.closePath();\n    }\n\n    return boundingBox;\n  }\n\n}\n\nclass PatternElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'pattern';\n  }\n\n  createPattern(ctx, _, parentOpacityProp) {\n    var width = this.getStyle('width').getPixels('x', true);\n    var height = this.getStyle('height').getPixels('y', true); // render me using a temporary svg element\n\n    var patternSvg = new SVGElement(this.document, null);\n    patternSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    patternSvg.attributes.width = new Property(this.document, 'width', \"\".concat(width, \"px\"));\n    patternSvg.attributes.height = new Property(this.document, 'height', \"\".concat(height, \"px\"));\n    patternSvg.attributes.transform = new Property(this.document, 'transform', this.getAttribute('patternTransform').getValue());\n    patternSvg.children = this.children;\n    var patternCanvas = this.document.createCanvas(width, height);\n    var patternCtx = patternCanvas.getContext('2d');\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n\n    if (xAttr.hasValue() && yAttr.hasValue()) {\n      patternCtx.translate(xAttr.getPixels('x', true), yAttr.getPixels('y', true));\n    }\n\n    if (parentOpacityProp.hasValue()) {\n      this.styles['fill-opacity'] = parentOpacityProp;\n    } else {\n      Reflect.deleteProperty(this.styles, 'fill-opacity');\n    } // render 3x3 grid so when we transform there's no white space on edges\n\n\n    for (var x = -1; x <= 1; x++) {\n      for (var y = -1; y <= 1; y++) {\n        patternCtx.save();\n        patternSvg.attributes.x = new Property(this.document, 'x', x * patternCanvas.width);\n        patternSvg.attributes.y = new Property(this.document, 'y', y * patternCanvas.height);\n        patternSvg.render(patternCtx);\n        patternCtx.restore();\n      }\n    }\n\n    var pattern = ctx.createPattern(patternCanvas, 'repeat');\n    return pattern;\n  }\n\n}\n\nclass MarkerElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'marker';\n  }\n\n  render(ctx, point, angle) {\n    if (!point) {\n      return;\n    }\n\n    var {\n      x,\n      y\n    } = point;\n    var orient = this.getAttribute('orient').getString('auto');\n    var markerUnits = this.getAttribute('markerUnits').getString('strokeWidth');\n    ctx.translate(x, y);\n\n    if (orient === 'auto') {\n      ctx.rotate(angle);\n    }\n\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(ctx.lineWidth, ctx.lineWidth);\n    }\n\n    ctx.save(); // render me using a temporary svg element\n\n    var markerSvg = new SVGElement(this.document, null);\n    markerSvg.type = this.type;\n    markerSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    markerSvg.attributes.refX = new Property(this.document, 'refX', this.getAttribute('refX').getValue());\n    markerSvg.attributes.refY = new Property(this.document, 'refY', this.getAttribute('refY').getValue());\n    markerSvg.attributes.width = new Property(this.document, 'width', this.getAttribute('markerWidth').getValue());\n    markerSvg.attributes.height = new Property(this.document, 'height', this.getAttribute('markerHeight').getValue());\n    markerSvg.attributes.overflow = new Property(this.document, 'overflow', this.getAttribute('overflow').getValue());\n    markerSvg.attributes.fill = new Property(this.document, 'fill', this.getAttribute('fill').getColor('black'));\n    markerSvg.attributes.stroke = new Property(this.document, 'stroke', this.getAttribute('stroke').getValue('none'));\n    markerSvg.children = this.children;\n    markerSvg.render(ctx);\n    ctx.restore();\n\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(1 / ctx.lineWidth, 1 / ctx.lineWidth);\n    }\n\n    if (orient === 'auto') {\n      ctx.rotate(-angle);\n    }\n\n    ctx.translate(-x, -y);\n  }\n\n}\n\nclass DefsElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'defs';\n  }\n\n  render() {// NOOP\n  }\n\n}\n\nclass GElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'g';\n  }\n\n  getBoundingBox(ctx) {\n    var boundingBox = new BoundingBox();\n    this.children.forEach(child => {\n      boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n    });\n    return boundingBox;\n  }\n\n}\n\nclass GradientElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.attributesToInherit = ['gradientUnits'];\n    this.stops = [];\n    var {\n      stops,\n      children\n    } = this;\n    children.forEach(child => {\n      if (child.type === 'stop') {\n        stops.push(child);\n      }\n    });\n  }\n\n  getGradientUnits() {\n    return this.getAttribute('gradientUnits').getString('objectBoundingBox');\n  }\n\n  createGradient(ctx, element, parentOpacityProp) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n    var stopsContainer = this;\n\n    if (this.getHrefAttribute().hasValue()) {\n      stopsContainer = this.getHrefAttribute().getDefinition();\n      this.inheritStopContainer(stopsContainer);\n    }\n\n    var {\n      stops\n    } = stopsContainer;\n    var gradient = this.getGradient(ctx, element);\n\n    if (!gradient) {\n      return this.addParentOpacity(parentOpacityProp, stops[stops.length - 1].color);\n    }\n\n    stops.forEach(stop => {\n      gradient.addColorStop(stop.offset, this.addParentOpacity(parentOpacityProp, stop.color));\n    });\n\n    if (this.getAttribute('gradientTransform').hasValue()) {\n      // render as transformed pattern on temporary canvas\n      var {\n        document\n      } = this;\n      var {\n        MAX_VIRTUAL_PIXELS,\n        viewPort\n      } = document.screen;\n      var [rootView] = viewPort.viewPorts;\n      var rect = new RectElement(document, null);\n      rect.attributes.x = new Property(document, 'x', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.y = new Property(document, 'y', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.width = new Property(document, 'width', MAX_VIRTUAL_PIXELS);\n      rect.attributes.height = new Property(document, 'height', MAX_VIRTUAL_PIXELS);\n      var group = new GElement(document, null);\n      group.attributes.transform = new Property(document, 'transform', this.getAttribute('gradientTransform').getValue());\n      group.children = [rect];\n      var patternSvg = new SVGElement(document, null);\n      patternSvg.attributes.x = new Property(document, 'x', 0);\n      patternSvg.attributes.y = new Property(document, 'y', 0);\n      patternSvg.attributes.width = new Property(document, 'width', rootView.width);\n      patternSvg.attributes.height = new Property(document, 'height', rootView.height);\n      patternSvg.children = [group];\n      var patternCanvas = document.createCanvas(rootView.width, rootView.height);\n      var patternCtx = patternCanvas.getContext('2d');\n      patternCtx.fillStyle = gradient;\n      patternSvg.render(patternCtx);\n      return patternCtx.createPattern(patternCanvas, 'no-repeat');\n    }\n\n    return gradient;\n  }\n\n  inheritStopContainer(stopsContainer) {\n    this.attributesToInherit.forEach(attributeToInherit => {\n      if (!this.getAttribute(attributeToInherit).hasValue() && stopsContainer.getAttribute(attributeToInherit).hasValue()) {\n        this.getAttribute(attributeToInherit, true).setValue(stopsContainer.getAttribute(attributeToInherit).getValue());\n      }\n    });\n  }\n\n  addParentOpacity(parentOpacityProp, color) {\n    if (parentOpacityProp.hasValue()) {\n      var colorProp = new Property(this.document, 'color', color);\n      return colorProp.addOpacity(parentOpacityProp).getColor();\n    }\n\n    return color;\n  }\n\n}\n\nclass LinearGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'linearGradient';\n    this.attributesToInherit.push('x1', 'y1', 'x2', 'y2');\n  }\n\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = isBoundingBoxUnits ? element.getBoundingBox(ctx) : null;\n\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n\n    if (!this.getAttribute('x1').hasValue() && !this.getAttribute('y1').hasValue() && !this.getAttribute('x2').hasValue() && !this.getAttribute('y2').hasValue()) {\n      this.getAttribute('x1', true).setValue(0);\n      this.getAttribute('y1', true).setValue(0);\n      this.getAttribute('x2', true).setValue(1);\n      this.getAttribute('y2', true).setValue(0);\n    }\n\n    var x1 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x1').getNumber() : this.getAttribute('x1').getPixels('x');\n    var y1 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y1').getNumber() : this.getAttribute('y1').getPixels('y');\n    var x2 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x2').getNumber() : this.getAttribute('x2').getPixels('x');\n    var y2 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y2').getNumber() : this.getAttribute('y2').getPixels('y');\n\n    if (x1 === x2 && y1 === y2) {\n      return null;\n    }\n\n    return ctx.createLinearGradient(x1, y1, x2, y2);\n  }\n\n}\n\nclass RadialGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'radialGradient';\n    this.attributesToInherit.push('cx', 'cy', 'r', 'fx', 'fy', 'fr');\n  }\n\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = element.getBoundingBox(ctx);\n\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n\n    if (!this.getAttribute('cx').hasValue()) {\n      this.getAttribute('cx', true).setValue('50%');\n    }\n\n    if (!this.getAttribute('cy').hasValue()) {\n      this.getAttribute('cy', true).setValue('50%');\n    }\n\n    if (!this.getAttribute('r').hasValue()) {\n      this.getAttribute('r', true).setValue('50%');\n    }\n\n    var cx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('cx').getNumber() : this.getAttribute('cx').getPixels('x');\n    var cy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('cy').getNumber() : this.getAttribute('cy').getPixels('y');\n    var fx = cx;\n    var fy = cy;\n\n    if (this.getAttribute('fx').hasValue()) {\n      fx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('fx').getNumber() : this.getAttribute('fx').getPixels('x');\n    }\n\n    if (this.getAttribute('fy').hasValue()) {\n      fy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('fy').getNumber() : this.getAttribute('fy').getPixels('y');\n    }\n\n    var r = isBoundingBoxUnits ? (boundingBox.width + boundingBox.height) / 2.0 * this.getAttribute('r').getNumber() : this.getAttribute('r').getPixels();\n    var fr = this.getAttribute('fr').getPixels();\n    return ctx.createRadialGradient(fx, fy, fr, cx, cy, r);\n  }\n\n}\n\nclass StopElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'stop';\n    var offset = Math.max(0, Math.min(1, this.getAttribute('offset').getNumber()));\n    var stopOpacity = this.getStyle('stop-opacity');\n    var stopColor = this.getStyle('stop-color', true);\n\n    if (stopColor.getString() === '') {\n      stopColor.setValue('#000');\n    }\n\n    if (stopOpacity.hasValue()) {\n      stopColor = stopColor.addOpacity(stopOpacity);\n    }\n\n    this.offset = offset;\n    this.color = stopColor.getColor();\n  }\n\n}\n\nclass AnimateElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'animate';\n    this.duration = 0;\n    this.initialValue = null;\n    this.initialUnits = '';\n    this.removed = false;\n    this.frozen = false;\n    document.screen.animations.push(this);\n    this.begin = this.getAttribute('begin').getMilliseconds();\n    this.maxDuration = this.begin + this.getAttribute('dur').getMilliseconds();\n    this.from = this.getAttribute('from');\n    this.to = this.getAttribute('to');\n    this.values = new Property(document, 'values', null);\n    var valuesAttr = this.getAttribute('values');\n\n    if (valuesAttr.hasValue()) {\n      this.values.setValue(valuesAttr.getString().split(';'));\n    }\n  }\n\n  getProperty() {\n    var attributeType = this.getAttribute('attributeType').getString();\n    var attributeName = this.getAttribute('attributeName').getString();\n\n    if (attributeType === 'CSS') {\n      return this.parent.getStyle(attributeName, true);\n    }\n\n    return this.parent.getAttribute(attributeName, true);\n  }\n\n  calcValue() {\n    var {\n      initialUnits\n    } = this;\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var newValue = from.getNumber() + (to.getNumber() - from.getNumber()) * progress;\n\n    if (initialUnits === '%') {\n      newValue *= 100.0; // numValue() returns 0-1 whereas properties are 0-100\n    }\n\n    return \"\".concat(newValue).concat(initialUnits);\n  }\n\n  update(delta) {\n    var {\n      parent\n    } = this;\n    var prop = this.getProperty(); // set initial value\n\n    if (!this.initialValue) {\n      this.initialValue = prop.getString();\n      this.initialUnits = prop.getUnits();\n    } // if we're past the end time\n\n\n    if (this.duration > this.maxDuration) {\n      var fill = this.getAttribute('fill').getString('remove'); // loop for indefinitely repeating animations\n\n      if (this.getAttribute('repeatCount').getString() === 'indefinite' || this.getAttribute('repeatDur').getString() === 'indefinite') {\n        this.duration = 0;\n      } else if (fill === 'freeze' && !this.frozen) {\n        this.frozen = true;\n        parent.animationFrozen = true;\n        parent.animationFrozenValue = prop.getString();\n      } else if (fill === 'remove' && !this.removed) {\n        this.removed = true;\n        prop.setValue(parent.animationFrozen ? parent.animationFrozenValue : this.initialValue);\n        return true;\n      }\n\n      return false;\n    }\n\n    this.duration += delta; // if we're past the begin time\n\n    var updated = false;\n\n    if (this.begin < this.duration) {\n      var newValue = this.calcValue(); // tween\n\n      var typeAttr = this.getAttribute('type');\n\n      if (typeAttr.hasValue()) {\n        // for transform, etc.\n        var type = typeAttr.getString();\n        newValue = \"\".concat(type, \"(\").concat(newValue, \")\");\n      }\n\n      prop.setValue(newValue);\n      updated = true;\n    }\n\n    return updated;\n  }\n\n  getProgress() {\n    var {\n      document,\n      values\n    } = this;\n    var result = {\n      progress: (this.duration - this.begin) / (this.maxDuration - this.begin)\n    };\n\n    if (values.hasValue()) {\n      var p = result.progress * (values.getValue().length - 1);\n      var lb = Math.floor(p);\n      var ub = Math.ceil(p);\n      result.from = new Property(document, 'from', parseFloat(values.getValue()[lb]));\n      result.to = new Property(document, 'to', parseFloat(values.getValue()[ub]));\n      result.progress = (p - lb) / (ub - lb);\n    } else {\n      result.from = this.from;\n      result.to = this.to;\n    }\n\n    return result;\n  }\n\n}\n\nclass AnimateColorElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateColor';\n  }\n\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress();\n    var colorFrom = new rgbcolor__WEBPACK_IMPORTED_MODULE_13__(from.getColor());\n    var colorTo = new rgbcolor__WEBPACK_IMPORTED_MODULE_13__(to.getColor());\n\n    if (colorFrom.ok && colorTo.ok) {\n      // tween color linearly\n      var r = colorFrom.r + (colorTo.r - colorFrom.r) * progress;\n      var g = colorFrom.g + (colorTo.g - colorFrom.g) * progress;\n      var b = colorFrom.b + (colorTo.b - colorFrom.b) * progress; // ? alpha\n\n      return \"rgb(\".concat(Math.floor(r), \", \").concat(Math.floor(g), \", \").concat(Math.floor(b), \")\");\n    }\n\n    return this.getAttribute('from').getColor();\n  }\n\n}\n\nclass AnimateTransformElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateTransform';\n  }\n\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var transformFrom = toNumbers(from.getString());\n    var transformTo = toNumbers(to.getString());\n    var newValue = transformFrom.map((from, i) => {\n      var to = transformTo[i];\n      return from + (to - from) * progress;\n    }).join(' ');\n    return newValue;\n  }\n\n}\n\nclass FontElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font';\n    this.glyphs = Object.create(null);\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    var {\n      definitions\n    } = document;\n    var {\n      children\n    } = this;\n\n    for (var child of children) {\n      switch (child.type) {\n        case 'font-face':\n          {\n            this.fontFace = child;\n            var fontFamilyStyle = child.getStyle('font-family');\n\n            if (fontFamilyStyle.hasValue()) {\n              definitions[fontFamilyStyle.getString()] = this;\n            }\n\n            break;\n          }\n\n        case 'missing-glyph':\n          this.missingGlyph = child;\n          break;\n\n        case 'glyph':\n          {\n            var glyph = child;\n\n            if (glyph.arabicForm) {\n              this.isRTL = true;\n              this.isArabic = true;\n\n              if (typeof this.glyphs[glyph.unicode] === 'undefined') {\n                this.glyphs[glyph.unicode] = Object.create(null);\n              }\n\n              this.glyphs[glyph.unicode][glyph.arabicForm] = glyph;\n            } else {\n              this.glyphs[glyph.unicode] = glyph;\n            }\n\n            break;\n          }\n      }\n    }\n  }\n\n  render() {// NO RENDER\n  }\n\n}\n\nclass FontFaceElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font-face';\n    this.ascent = this.getAttribute('ascent').getNumber();\n    this.descent = this.getAttribute('descent').getNumber();\n    this.unitsPerEm = this.getAttribute('units-per-em').getNumber();\n  }\n\n}\n\nclass MissingGlyphElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'missing-glyph';\n    this.horizAdvX = 0;\n  }\n\n}\n\nclass TRefElement extends TextElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'tref';\n  }\n\n  getText() {\n    var element = this.getHrefAttribute().getDefinition();\n\n    if (element) {\n      var firstChild = element.children[0];\n\n      if (firstChild) {\n        return firstChild.getText();\n      }\n    }\n\n    return '';\n  }\n\n}\n\nclass AElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'a';\n    var {\n      childNodes\n    } = node;\n    var firstChild = childNodes[0];\n    var hasText = childNodes.length > 0 && Array.from(childNodes).every(node => node.nodeType === 3);\n    this.hasText = hasText;\n    this.text = hasText ? this.getTextFromNode(firstChild) : '';\n  }\n\n  getText() {\n    return this.text;\n  }\n\n  renderChildren(ctx) {\n    if (this.hasText) {\n      // render as text element\n      super.renderChildren(ctx);\n      var {\n        document,\n        x,\n        y\n      } = this;\n      var {\n        mouse\n      } = document.screen;\n      var fontSize = new Property(document, 'fontSize', Font.parse(document.ctx.font).fontSize); // Do not calc bounding box if mouse is not working.\n\n      if (mouse.isWorking()) {\n        mouse.checkBoundingBox(this, new BoundingBox(x, y - fontSize.getPixels('y'), x + this.measureText(ctx), y));\n      }\n    } else if (this.children.length > 0) {\n      // render as temporary group\n      var g = new GElement(this.document, null);\n      g.children = this.children;\n      g.parent = this;\n      g.render(ctx);\n    }\n  }\n\n  onClick() {\n    var {\n      window\n    } = this.document;\n\n    if (window) {\n      window.open(this.getHrefAttribute().getString());\n    }\n  }\n\n  onMouseMove() {\n    var ctx = this.document.ctx;\n    ctx.canvas.style.cursor = 'pointer';\n  }\n\n}\n\nfunction ownKeys$2(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$2(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys$2(Object(source), true).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys$2(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\nclass TextPathElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'textPath';\n    this.textWidth = 0;\n    this.textHeight = 0;\n    this.pathLength = -1;\n    this.glyphInfo = null;\n    this.letterSpacingCache = [];\n    this.measuresCache = new Map([['', 0]]);\n    var pathElement = this.getHrefAttribute().getDefinition();\n    this.text = this.getTextFromNode();\n    this.dataArray = this.parsePathData(pathElement);\n  }\n\n  getText() {\n    return this.text;\n  }\n\n  path(ctx) {\n    var {\n      dataArray\n    } = this;\n\n    if (ctx) {\n      ctx.beginPath();\n    }\n\n    dataArray.forEach(_ref => {\n      var {\n        type,\n        points\n      } = _ref;\n\n      switch (type) {\n        case PathParser.LINE_TO:\n          if (ctx) {\n            ctx.lineTo(points[0], points[1]);\n          }\n\n          break;\n\n        case PathParser.MOVE_TO:\n          if (ctx) {\n            ctx.moveTo(points[0], points[1]);\n          }\n\n          break;\n\n        case PathParser.CURVE_TO:\n          if (ctx) {\n            ctx.bezierCurveTo(points[0], points[1], points[2], points[3], points[4], points[5]);\n          }\n\n          break;\n\n        case PathParser.QUAD_TO:\n          if (ctx) {\n            ctx.quadraticCurveTo(points[0], points[1], points[2], points[3]);\n          }\n\n          break;\n\n        case PathParser.ARC:\n          {\n            var [cx, cy, rx, ry, theta, dTheta, psi, fs] = points;\n            var r = rx > ry ? rx : ry;\n            var scaleX = rx > ry ? 1 : rx / ry;\n            var scaleY = rx > ry ? ry / rx : 1;\n\n            if (ctx) {\n              ctx.translate(cx, cy);\n              ctx.rotate(psi);\n              ctx.scale(scaleX, scaleY);\n              ctx.arc(0, 0, r, theta, theta + dTheta, Boolean(1 - fs));\n              ctx.scale(1 / scaleX, 1 / scaleY);\n              ctx.rotate(-psi);\n              ctx.translate(-cx, -cy);\n            }\n\n            break;\n          }\n\n        case PathParser.CLOSE_PATH:\n          if (ctx) {\n            ctx.closePath();\n          }\n\n          break;\n      }\n    });\n  }\n\n  renderChildren(ctx) {\n    this.setTextData(ctx);\n    ctx.save();\n    var textDecoration = this.parent.getStyle('text-decoration').getString();\n    var fontSize = this.getFontSize();\n    var {\n      glyphInfo\n    } = this;\n    var fill = ctx.fillStyle;\n\n    if (textDecoration === 'underline') {\n      ctx.beginPath();\n    }\n\n    glyphInfo.forEach((glyph, i) => {\n      var {\n        p0,\n        p1,\n        rotation,\n        text: partialText\n      } = glyph;\n      ctx.save();\n      ctx.translate(p0.x, p0.y);\n      ctx.rotate(rotation);\n\n      if (ctx.fillStyle) {\n        ctx.fillText(partialText, 0, 0);\n      }\n\n      if (ctx.strokeStyle) {\n        ctx.strokeText(partialText, 0, 0);\n      }\n\n      ctx.restore();\n\n      if (textDecoration === 'underline') {\n        if (i === 0) {\n          ctx.moveTo(p0.x, p0.y + fontSize / 8);\n        }\n\n        ctx.lineTo(p1.x, p1.y + fontSize / 5);\n      } // // To assist with debugging visually, uncomment following\n      //\n      // ctx.beginPath();\n      // if (i % 2)\n      // \tctx.strokeStyle = 'red';\n      // else\n      // \tctx.strokeStyle = 'green';\n      // ctx.moveTo(p0.x, p0.y);\n      // ctx.lineTo(p1.x, p1.y);\n      // ctx.stroke();\n      // ctx.closePath();\n\n    });\n\n    if (textDecoration === 'underline') {\n      ctx.lineWidth = fontSize / 20;\n      ctx.strokeStyle = fill;\n      ctx.stroke();\n      ctx.closePath();\n    }\n\n    ctx.restore();\n  }\n\n  getLetterSpacingAt() {\n    var idx = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    return this.letterSpacingCache[idx] || 0;\n  }\n\n  findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, inputOffset, dy, c, charI) {\n    var offset = inputOffset;\n    var glyphWidth = this.measureText(ctx, c);\n\n    if (c === ' ' && anchor === 'justify' && textFullWidth < fullPathWidth) {\n      glyphWidth += (fullPathWidth - textFullWidth) / spacesNumber;\n    }\n\n    if (charI > -1) {\n      offset += this.getLetterSpacingAt(charI);\n    }\n\n    var splineStep = this.textHeight / 20;\n    var p0 = this.getEquidistantPointOnPath(offset, splineStep, 0);\n    var p1 = this.getEquidistantPointOnPath(offset + glyphWidth, splineStep, 0);\n    var segment = {\n      p0,\n      p1\n    };\n    var rotation = p0 && p1 ? Math.atan2(p1.y - p0.y, p1.x - p0.x) : 0;\n\n    if (dy) {\n      var dyX = Math.cos(Math.PI / 2 + rotation) * dy;\n      var dyY = Math.cos(-rotation) * dy;\n      segment.p0 = _objectSpread$2(_objectSpread$2({}, p0), {}, {\n        x: p0.x + dyX,\n        y: p0.y + dyY\n      });\n      segment.p1 = _objectSpread$2(_objectSpread$2({}, p1), {}, {\n        x: p1.x + dyX,\n        y: p1.y + dyY\n      });\n    }\n\n    offset += glyphWidth;\n    return {\n      offset,\n      segment,\n      rotation\n    };\n  }\n\n  measureText(ctx, text) {\n    var {\n      measuresCache\n    } = this;\n    var targetText = text || this.getText();\n\n    if (measuresCache.has(targetText)) {\n      return measuresCache.get(targetText);\n    }\n\n    var measure = this.measureTargetText(ctx, targetText);\n    measuresCache.set(targetText, measure);\n    return measure;\n  } // This method supposes what all custom fonts already loaded.\n  // If some font will be loaded after this method call, <textPath> will not be rendered correctly.\n  // You need to call this method manually to update glyphs cache.\n\n\n  setTextData(ctx) {\n    if (this.glyphInfo) {\n      return;\n    }\n\n    var renderText = this.getText();\n    var chars = renderText.split('');\n    var spacesNumber = renderText.split(' ').length - 1;\n    var dx = this.parent.getAttribute('dx').split().map(_ => _.getPixels('x'));\n    var dy = this.parent.getAttribute('dy').getPixels('y');\n    var anchor = this.parent.getStyle('text-anchor').getString('start');\n    var thisSpacing = this.getStyle('letter-spacing');\n    var parentSpacing = this.parent.getStyle('letter-spacing');\n    var letterSpacing = 0;\n\n    if (!thisSpacing.hasValue() || thisSpacing.getValue() === 'inherit') {\n      letterSpacing = parentSpacing.getPixels();\n    } else if (thisSpacing.hasValue()) {\n      if (thisSpacing.getValue() !== 'initial' && thisSpacing.getValue() !== 'unset') {\n        letterSpacing = thisSpacing.getPixels();\n      }\n    } // fill letter-spacing cache\n\n\n    var letterSpacingCache = [];\n    var textLen = renderText.length;\n    this.letterSpacingCache = letterSpacingCache;\n\n    for (var i = 0; i < textLen; i++) {\n      letterSpacingCache.push(typeof dx[i] !== 'undefined' ? dx[i] : letterSpacing);\n    }\n\n    var dxSum = letterSpacingCache.reduce((acc, cur, i) => i === 0 ? 0 : acc + cur || 0, 0);\n    var textWidth = this.measureText(ctx);\n    var textFullWidth = Math.max(textWidth + dxSum, 0);\n    this.textWidth = textWidth;\n    this.textHeight = this.getFontSize();\n    this.glyphInfo = [];\n    var fullPathWidth = this.getPathLength();\n    var startOffset = this.getStyle('startOffset').getNumber(0) * fullPathWidth;\n    var offset = 0;\n\n    if (anchor === 'middle' || anchor === 'center') {\n      offset = -textFullWidth / 2;\n    }\n\n    if (anchor === 'end' || anchor === 'right') {\n      offset = -textFullWidth;\n    }\n\n    offset += startOffset;\n    chars.forEach((char, i) => {\n      // Find such segment what distance between p0 and p1 is approx. width of glyph\n      var {\n        offset: nextOffset,\n        segment,\n        rotation\n      } = this.findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, offset, dy, char, i);\n      offset = nextOffset;\n\n      if (!segment.p0 || !segment.p1) {\n        return;\n      } // const width = this.getLineLength(\n      // \tsegment.p0.x,\n      // \tsegment.p0.y,\n      // \tsegment.p1.x,\n      // \tsegment.p1.y\n      // );\n      // Note: Since glyphs are rendered one at a time, any kerning pair data built into the font will not be used.\n      // Can foresee having a rough pair table built in that the developer can override as needed.\n      // Or use \"dx\" attribute of the <text> node as a naive replacement\n      // const kern = 0;\n      // placeholder for future implementation\n      // const midpoint = this.getPointOnLine(\n      // \tkern + width / 2.0,\n      // \tsegment.p0.x, segment.p0.y, segment.p1.x, segment.p1.y\n      // );\n\n\n      this.glyphInfo.push({\n        // transposeX: midpoint.x,\n        // transposeY: midpoint.y,\n        text: chars[i],\n        p0: segment.p0,\n        p1: segment.p1,\n        rotation\n      });\n    });\n  }\n\n  parsePathData(path) {\n    this.pathLength = -1; // reset path length\n\n    if (!path) {\n      return [];\n    }\n\n    var pathCommands = [];\n    var {\n      pathParser\n    } = path;\n    pathParser.reset(); // convert l, H, h, V, and v to L\n\n    while (!pathParser.isEnd()) {\n      var {\n        current\n      } = pathParser;\n      var startX = current ? current.x : 0;\n      var startY = current ? current.y : 0;\n      var command = pathParser.next();\n      var nextCommandType = command.type;\n      var points = [];\n\n      switch (command.type) {\n        case PathParser.MOVE_TO:\n          this.pathM(pathParser, points);\n          break;\n\n        case PathParser.LINE_TO:\n          nextCommandType = this.pathL(pathParser, points);\n          break;\n\n        case PathParser.HORIZ_LINE_TO:\n          nextCommandType = this.pathH(pathParser, points);\n          break;\n\n        case PathParser.VERT_LINE_TO:\n          nextCommandType = this.pathV(pathParser, points);\n          break;\n\n        case PathParser.CURVE_TO:\n          this.pathC(pathParser, points);\n          break;\n\n        case PathParser.SMOOTH_CURVE_TO:\n          nextCommandType = this.pathS(pathParser, points);\n          break;\n\n        case PathParser.QUAD_TO:\n          this.pathQ(pathParser, points);\n          break;\n\n        case PathParser.SMOOTH_QUAD_TO:\n          nextCommandType = this.pathT(pathParser, points);\n          break;\n\n        case PathParser.ARC:\n          points = this.pathA(pathParser);\n          break;\n\n        case PathParser.CLOSE_PATH:\n          PathElement.pathZ(pathParser);\n          break;\n      }\n\n      if (command.type !== PathParser.CLOSE_PATH) {\n        pathCommands.push({\n          type: nextCommandType,\n          points,\n          start: {\n            x: startX,\n            y: startY\n          },\n          pathLength: this.calcLength(startX, startY, nextCommandType, points)\n        });\n      } else {\n        pathCommands.push({\n          type: PathParser.CLOSE_PATH,\n          points: [],\n          pathLength: 0\n        });\n      }\n    }\n\n    return pathCommands;\n  }\n\n  pathM(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathM(pathParser).point;\n    points.push(x, y);\n  }\n\n  pathL(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathL(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathH(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathH(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathV(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathV(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathC(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n\n  pathS(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.CURVE_TO;\n  }\n\n  pathQ(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n\n  pathT(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.QUAD_TO;\n  }\n\n  pathA(pathParser) {\n    var {\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser);\n\n    if (sweepFlag === 0 && ad > 0) {\n      ad -= 2 * Math.PI;\n    }\n\n    if (sweepFlag === 1 && ad < 0) {\n      ad += 2 * Math.PI;\n    }\n\n    return [centp.x, centp.y, rX, rY, a1, ad, xAxisRotation, sweepFlag];\n  }\n\n  calcLength(x, y, commandType, points) {\n    var len = 0;\n    var p1 = null;\n    var p2 = null;\n    var t = 0;\n\n    switch (commandType) {\n      case PathParser.LINE_TO:\n        return this.getLineLength(x, y, points[0], points[1]);\n\n      case PathParser.CURVE_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnCubicBezier(0, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnCubicBezier(t, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n\n        return len;\n\n      case PathParser.QUAD_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnQuadraticBezier(0, x, y, points[0], points[1], points[2], points[3]);\n\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnQuadraticBezier(t, x, y, points[0], points[1], points[2], points[3]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n\n        return len;\n\n      case PathParser.ARC:\n        {\n          // Approximates by breaking curve into line segments\n          len = 0.0;\n          var start = points[4]; // 4 = theta\n\n          var dTheta = points[5]; // 5 = dTheta\n\n          var end = points[4] + dTheta;\n          var inc = Math.PI / 180.0; // 1 degree resolution\n\n          if (Math.abs(start - end) < inc) {\n            inc = Math.abs(start - end);\n          } // Note: for purpose of calculating arc length, not going to worry about rotating X-axis by angle psi\n\n\n          p1 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], start, 0);\n\n          if (dTheta < 0) {\n            // clockwise\n            for (t = start - inc; t > end; t -= inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          } else {\n            // counter-clockwise\n            for (t = start + inc; t < end; t += inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          }\n\n          p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], end, 0);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          return len;\n        }\n    }\n\n    return 0;\n  }\n\n  getPointOnLine(dist, p1x, p1y, p2x, p2y) {\n    var fromX = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : p1x;\n    var fromY = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : p1y;\n    var m = (p2y - p1y) / (p2x - p1x + PSEUDO_ZERO);\n    var run = Math.sqrt(dist * dist / (1 + m * m));\n\n    if (p2x < p1x) {\n      run *= -1;\n    }\n\n    var rise = m * run;\n    var pt = null;\n\n    if (p2x === p1x) {\n      // vertical line\n      pt = {\n        x: fromX,\n        y: fromY + rise\n      };\n    } else if ((fromY - p1y) / (fromX - p1x + PSEUDO_ZERO) === m) {\n      pt = {\n        x: fromX + run,\n        y: fromY + rise\n      };\n    } else {\n      var ix = 0;\n      var iy = 0;\n      var len = this.getLineLength(p1x, p1y, p2x, p2y);\n\n      if (len < PSEUDO_ZERO) {\n        return null;\n      }\n\n      var u = (fromX - p1x) * (p2x - p1x) + (fromY - p1y) * (p2y - p1y);\n      u /= len * len;\n      ix = p1x + u * (p2x - p1x);\n      iy = p1y + u * (p2y - p1y);\n      var pRise = this.getLineLength(fromX, fromY, ix, iy);\n      var pRun = Math.sqrt(dist * dist - pRise * pRise);\n      run = Math.sqrt(pRun * pRun / (1 + m * m));\n\n      if (p2x < p1x) {\n        run *= -1;\n      }\n\n      rise = m * run;\n      pt = {\n        x: ix + run,\n        y: iy + rise\n      };\n    }\n\n    return pt;\n  }\n\n  getPointOnPath(distance) {\n    var fullLen = this.getPathLength();\n    var cumulativePathLength = 0;\n    var p = null;\n\n    if (distance < -0.00005 || distance - 0.00005 > fullLen) {\n      return null;\n    }\n\n    var {\n      dataArray\n    } = this;\n\n    for (var command of dataArray) {\n      if (command && (command.pathLength < 0.00005 || cumulativePathLength + command.pathLength + 0.00005 < distance)) {\n        cumulativePathLength += command.pathLength;\n        continue;\n      }\n\n      var delta = distance - cumulativePathLength;\n      var currentT = 0;\n\n      switch (command.type) {\n        case PathParser.LINE_TO:\n          p = this.getPointOnLine(delta, command.start.x, command.start.y, command.points[0], command.points[1], command.start.x, command.start.y);\n          break;\n\n        case PathParser.ARC:\n          {\n            var start = command.points[4]; // 4 = theta\n\n            var dTheta = command.points[5]; // 5 = dTheta\n\n            var end = command.points[4] + dTheta;\n            currentT = start + delta / command.pathLength * dTheta;\n\n            if (dTheta < 0 && currentT < end || dTheta >= 0 && currentT > end) {\n              break;\n            }\n\n            p = this.getPointOnEllipticalArc(command.points[0], command.points[1], command.points[2], command.points[3], currentT, command.points[6]);\n            break;\n          }\n\n        case PathParser.CURVE_TO:\n          currentT = delta / command.pathLength;\n\n          if (currentT > 1) {\n            currentT = 1;\n          }\n\n          p = this.getPointOnCubicBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3], command.points[4], command.points[5]);\n          break;\n\n        case PathParser.QUAD_TO:\n          currentT = delta / command.pathLength;\n\n          if (currentT > 1) {\n            currentT = 1;\n          }\n\n          p = this.getPointOnQuadraticBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3]);\n          break;\n      }\n\n      if (p) {\n        return p;\n      }\n\n      break;\n    }\n\n    return null;\n  }\n\n  getLineLength(x1, y1, x2, y2) {\n    return Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n  }\n\n  getPathLength() {\n    if (this.pathLength === -1) {\n      this.pathLength = this.dataArray.reduce((length, command) => command.pathLength > 0 ? length + command.pathLength : length, 0);\n    }\n\n    return this.pathLength;\n  }\n\n  getPointOnCubicBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y, p4x, p4y) {\n    var x = p4x * CB1(pct) + p3x * CB2(pct) + p2x * CB3(pct) + p1x * CB4(pct);\n    var y = p4y * CB1(pct) + p3y * CB2(pct) + p2y * CB3(pct) + p1y * CB4(pct);\n    return {\n      x,\n      y\n    };\n  }\n\n  getPointOnQuadraticBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y) {\n    var x = p3x * QB1(pct) + p2x * QB2(pct) + p1x * QB3(pct);\n    var y = p3y * QB1(pct) + p2y * QB2(pct) + p1y * QB3(pct);\n    return {\n      x,\n      y\n    };\n  }\n\n  getPointOnEllipticalArc(cx, cy, rx, ry, theta, psi) {\n    var cosPsi = Math.cos(psi);\n    var sinPsi = Math.sin(psi);\n    var pt = {\n      x: rx * Math.cos(theta),\n      y: ry * Math.sin(theta)\n    };\n    return {\n      x: cx + (pt.x * cosPsi - pt.y * sinPsi),\n      y: cy + (pt.x * sinPsi + pt.y * cosPsi)\n    };\n  } // TODO need some optimisations. possibly build cache only for curved segments?\n\n\n  buildEquidistantCache(inputStep, inputPrecision) {\n    var fullLen = this.getPathLength();\n    var precision = inputPrecision || 0.25; // accuracy vs performance\n\n    var step = inputStep || fullLen / 100;\n\n    if (!this.equidistantCache || this.equidistantCache.step !== step || this.equidistantCache.precision !== precision) {\n      // Prepare cache\n      this.equidistantCache = {\n        step,\n        precision,\n        points: []\n      }; // Calculate points\n\n      var s = 0;\n\n      for (var l = 0; l <= fullLen; l += precision) {\n        var p0 = this.getPointOnPath(l);\n        var p1 = this.getPointOnPath(l + precision);\n\n        if (!p0 || !p1) {\n          continue;\n        }\n\n        s += this.getLineLength(p0.x, p0.y, p1.x, p1.y);\n\n        if (s >= step) {\n          this.equidistantCache.points.push({\n            x: p0.x,\n            y: p0.y,\n            distance: l\n          });\n          s -= step;\n        }\n      }\n    }\n  }\n\n  getEquidistantPointOnPath(targetDistance, step, precision) {\n    this.buildEquidistantCache(step, precision);\n\n    if (targetDistance < 0 || targetDistance - this.getPathLength() > 0.00005) {\n      return null;\n    }\n\n    var idx = Math.round(targetDistance / this.getPathLength() * (this.equidistantCache.points.length - 1));\n    return this.equidistantCache.points[idx] || null;\n  }\n\n}\n\nvar dataUriRegex = /^\\s*data:(([^/,;]+\\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;\nclass ImageElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'image';\n    this.loaded = false;\n    var href = this.getHrefAttribute().getString();\n\n    if (!href) {\n      return;\n    }\n\n    var isSvg = href.endsWith('.svg') || /^\\s*data:image\\/svg\\+xml/i.test(href);\n    document.images.push(this);\n\n    if (!isSvg) {\n      void this.loadImage(href);\n    } else {\n      void this.loadSvg(href);\n    }\n\n    this.isSvg = isSvg;\n  }\n\n  loadImage(href) {\n    var _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      try {\n        var image = yield _this.document.createImage(href);\n        _this.image = image;\n      } catch (err) {\n        console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n      }\n\n      _this.loaded = true;\n    })();\n  }\n\n  loadSvg(href) {\n    var _this2 = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var match = dataUriRegex.exec(href);\n\n      if (match) {\n        var data = match[5];\n\n        if (match[4] === 'base64') {\n          _this2.image = atob(data);\n        } else {\n          _this2.image = decodeURIComponent(data);\n        }\n      } else {\n        try {\n          var response = yield _this2.document.fetch(href);\n          var svg = yield response.text();\n          _this2.image = svg;\n        } catch (err) {\n          console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n        }\n      }\n\n      _this2.loaded = true;\n    })();\n  }\n\n  renderChildren(ctx) {\n    var {\n      document,\n      image,\n      loaded\n    } = this;\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n\n    if (!loaded || !image || !width || !height) {\n      return;\n    }\n\n    ctx.save();\n    ctx.translate(x, y);\n\n    if (this.isSvg) {\n      var subDocument = document.canvg.forkString(ctx, this.image, {\n        ignoreMouse: true,\n        ignoreAnimation: true,\n        ignoreDimensions: true,\n        ignoreClear: true,\n        offsetX: 0,\n        offsetY: 0,\n        scaleWidth: width,\n        scaleHeight: height\n      });\n      subDocument.document.documentElement.parent = this;\n      void subDocument.render();\n    } else {\n      var _image = this.image;\n      document.setViewBox({\n        ctx,\n        aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n        width,\n        desiredWidth: _image.width,\n        height,\n        desiredHeight: _image.height\n      });\n\n      if (this.loaded) {\n        if (typeof _image.complete === 'undefined' || _image.complete) {\n          ctx.drawImage(_image, 0, 0);\n        }\n      }\n    }\n\n    ctx.restore();\n  }\n\n  getBoundingBox() {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n\n}\n\nclass SymbolElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'symbol';\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\n\nclass SVGFontLoader {\n  constructor(document) {\n    this.document = document;\n    this.loaded = false;\n    document.fonts.push(this);\n  }\n\n  load(fontFamily, url) {\n    var _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      try {\n        var {\n          document\n        } = _this;\n        var svgDocument = yield document.canvg.parser.load(url);\n        var fonts = svgDocument.getElementsByTagName('font');\n        Array.from(fonts).forEach(fontNode => {\n          var font = document.createElement(fontNode);\n          document.definitions[fontFamily] = font;\n        });\n      } catch (err) {\n        console.error(\"Error while loading font \\\"\".concat(url, \"\\\":\"), err);\n      }\n\n      _this.loaded = true;\n    })();\n  }\n\n}\n\nclass StyleElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'style';\n    var css = compressSpaces(Array.from(node.childNodes) // NEED TEST\n    .map(_ => _.textContent).join('').replace(/(\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*+\\/)|(^[\\s]*\\/\\/.*)/gm, '') // remove comments\n    .replace(/@import.*;/g, '') // remove imports\n    );\n    var cssDefs = css.split('}');\n    cssDefs.forEach(_ => {\n      var def = _.trim();\n\n      if (!def) {\n        return;\n      }\n\n      var cssParts = def.split('{');\n      var cssClasses = cssParts[0].split(',');\n      var cssProps = cssParts[1].split(';');\n      cssClasses.forEach(_ => {\n        var cssClass = _.trim();\n\n        if (!cssClass) {\n          return;\n        }\n\n        var props = document.styles[cssClass] || {};\n        cssProps.forEach(cssProp => {\n          var prop = cssProp.indexOf(':');\n          var name = cssProp.substr(0, prop).trim();\n          var value = cssProp.substr(prop + 1, cssProp.length - prop).trim();\n\n          if (name && value) {\n            props[name] = new Property(document, name, value);\n          }\n        });\n        document.styles[cssClass] = props;\n        document.stylesSpecificity[cssClass] = getSelectorSpecificity(cssClass);\n\n        if (cssClass === '@font-face') {\n          //  && !nodeEnv\n          var fontFamily = props['font-family'].getString().replace(/\"|'/g, '');\n          var srcs = props.src.getString().split(',');\n          srcs.forEach(src => {\n            if (src.indexOf('format(\"svg\")') > 0) {\n              var url = parseExternalUrl(src);\n\n              if (url) {\n                void new SVGFontLoader(document).load(fontFamily, url);\n              }\n            }\n          });\n        }\n      });\n    });\n  }\n\n}\nStyleElement.parseExternalUrl = parseExternalUrl;\n\nclass UseElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'use';\n  }\n\n  setContext(ctx) {\n    super.setContext(ctx);\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n\n    if (xAttr.hasValue()) {\n      ctx.translate(xAttr.getPixels('x'), 0);\n    }\n\n    if (yAttr.hasValue()) {\n      ctx.translate(0, yAttr.getPixels('y'));\n    }\n  }\n\n  path(ctx) {\n    var {\n      element\n    } = this;\n\n    if (element) {\n      element.path(ctx);\n    }\n  }\n\n  renderChildren(ctx) {\n    var {\n      document,\n      element\n    } = this;\n\n    if (element) {\n      var tempSvg = element;\n\n      if (element.type === 'symbol') {\n        // render me using a temporary svg element in symbol cases (http://www.w3.org/TR/SVG/struct.html#UseElement)\n        tempSvg = new SVGElement(document, null);\n        tempSvg.attributes.viewBox = new Property(document, 'viewBox', element.getAttribute('viewBox').getString());\n        tempSvg.attributes.preserveAspectRatio = new Property(document, 'preserveAspectRatio', element.getAttribute('preserveAspectRatio').getString());\n        tempSvg.attributes.overflow = new Property(document, 'overflow', element.getAttribute('overflow').getString());\n        tempSvg.children = element.children; // element is still the parent of the children\n\n        element.styles.opacity = new Property(document, 'opacity', this.calculateOpacity());\n      }\n\n      if (tempSvg.type === 'svg') {\n        var widthStyle = this.getStyle('width', false, true);\n        var heightStyle = this.getStyle('height', false, true); // if symbol or svg, inherit width/height from me\n\n        if (widthStyle.hasValue()) {\n          tempSvg.attributes.width = new Property(document, 'width', widthStyle.getString());\n        }\n\n        if (heightStyle.hasValue()) {\n          tempSvg.attributes.height = new Property(document, 'height', heightStyle.getString());\n        }\n      }\n\n      var oldParent = tempSvg.parent;\n      tempSvg.parent = this;\n      tempSvg.render(ctx);\n      tempSvg.parent = oldParent;\n    }\n  }\n\n  getBoundingBox(ctx) {\n    var {\n      element\n    } = this;\n\n    if (element) {\n      return element.getBoundingBox(ctx);\n    }\n\n    return null;\n  }\n\n  elementTransform() {\n    var {\n      document,\n      element\n    } = this;\n    return Transform.fromElement(document, element);\n  }\n\n  get element() {\n    if (!this.cachedElement) {\n      this.cachedElement = this.getHrefAttribute().getDefinition();\n    }\n\n    return this.cachedElement;\n  }\n\n}\n\nfunction imGet(img, x, y, width, _height, rgba) {\n  return img[y * width * 4 + x * 4 + rgba];\n}\n\nfunction imSet(img, x, y, width, _height, rgba, val) {\n  img[y * width * 4 + x * 4 + rgba] = val;\n}\n\nfunction m(matrix, i, v) {\n  var mi = matrix[i];\n  return mi * v;\n}\n\nfunction c(a, m1, m2, m3) {\n  return m1 + Math.cos(a) * m2 + Math.sin(a) * m3;\n}\n\nclass FeColorMatrixElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feColorMatrix';\n    var matrix = toNumbers(this.getAttribute('values').getString());\n\n    switch (this.getAttribute('type').getString('matrix')) {\n      // http://www.w3.org/TR/SVG/filters.html#feColorMatrixElement\n      case 'saturate':\n        {\n          var s = matrix[0];\n          /* eslint-disable array-element-newline */\n\n          matrix = [0.213 + 0.787 * s, 0.715 - 0.715 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 + 0.285 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 - 0.715 * s, 0.072 + 0.928 * s, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n\n      case 'hueRotate':\n        {\n          var a = matrix[0] * Math.PI / 180.0;\n          /* eslint-disable array-element-newline */\n\n          matrix = [c(a, 0.213, 0.787, -0.213), c(a, 0.715, -0.715, -0.715), c(a, 0.072, -0.072, 0.928), 0, 0, c(a, 0.213, -0.213, 0.143), c(a, 0.715, 0.285, 0.140), c(a, 0.072, -0.072, -0.283), 0, 0, c(a, 0.213, -0.213, -0.787), c(a, 0.715, -0.715, 0.715), c(a, 0.072, 0.928, 0.072), 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n\n      case 'luminanceToAlpha':\n        /* eslint-disable array-element-newline */\n        matrix = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2125, 0.7154, 0.0721, 0, 0, 0, 0, 0, 0, 1];\n        /* eslint-enable array-element-newline */\n\n        break;\n    }\n\n    this.matrix = matrix;\n    this.includeOpacity = this.getAttribute('includeOpacity').hasValue();\n  }\n\n  apply(ctx, _x, _y, width, height) {\n    // assuming x==0 && y==0 for now\n    var {\n      includeOpacity,\n      matrix\n    } = this;\n    var srcData = ctx.getImageData(0, 0, width, height);\n\n    for (var y = 0; y < height; y++) {\n      for (var x = 0; x < width; x++) {\n        var r = imGet(srcData.data, x, y, width, height, 0);\n        var g = imGet(srcData.data, x, y, width, height, 1);\n        var b = imGet(srcData.data, x, y, width, height, 2);\n        var a = imGet(srcData.data, x, y, width, height, 3);\n        var nr = m(matrix, 0, r) + m(matrix, 1, g) + m(matrix, 2, b) + m(matrix, 3, a) + m(matrix, 4, 1);\n        var ng = m(matrix, 5, r) + m(matrix, 6, g) + m(matrix, 7, b) + m(matrix, 8, a) + m(matrix, 9, 1);\n        var nb = m(matrix, 10, r) + m(matrix, 11, g) + m(matrix, 12, b) + m(matrix, 13, a) + m(matrix, 14, 1);\n        var na = m(matrix, 15, r) + m(matrix, 16, g) + m(matrix, 17, b) + m(matrix, 18, a) + m(matrix, 19, 1);\n\n        if (includeOpacity) {\n          nr = 0;\n          ng = 0;\n          nb = 0;\n          na *= a / 255;\n        }\n\n        imSet(srcData.data, x, y, width, height, 0, nr);\n        imSet(srcData.data, x, y, width, height, 1, ng);\n        imSet(srcData.data, x, y, width, height, 2, nb);\n        imSet(srcData.data, x, y, width, height, 3, na);\n      }\n    }\n\n    ctx.clearRect(0, 0, width, height);\n    ctx.putImageData(srcData, 0, 0);\n  }\n\n}\n\nclass MaskElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'mask';\n  }\n\n  apply(ctx, element) {\n    var {\n      document\n    } = this; // render as temp svg\n\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n\n    if (!width && !height) {\n      var boundingBox = new BoundingBox();\n      this.children.forEach(child => {\n        boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n      });\n      x = Math.floor(boundingBox.x1);\n      y = Math.floor(boundingBox.y1);\n      width = Math.floor(boundingBox.width);\n      height = Math.floor(boundingBox.height);\n    }\n\n    var ignoredStyles = this.removeStyles(element, MaskElement.ignoreStyles);\n    var maskCanvas = document.createCanvas(x + width, y + height);\n    var maskCtx = maskCanvas.getContext('2d');\n    document.screen.setDefaults(maskCtx);\n    this.renderChildren(maskCtx); // convert mask to alpha with a fake node\n    // TODO: refactor out apply from feColorMatrix\n\n    new FeColorMatrixElement(document, {\n      nodeType: 1,\n      childNodes: [],\n      attributes: [{\n        nodeName: 'type',\n        value: 'luminanceToAlpha'\n      }, {\n        nodeName: 'includeOpacity',\n        value: 'true'\n      }]\n    }).apply(maskCtx, 0, 0, x + width, y + height);\n    var tmpCanvas = document.createCanvas(x + width, y + height);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    element.render(tmpCtx);\n    tmpCtx.globalCompositeOperation = 'destination-in';\n    tmpCtx.fillStyle = maskCtx.createPattern(maskCanvas, 'no-repeat');\n    tmpCtx.fillRect(0, 0, x + width, y + height);\n    ctx.fillStyle = tmpCtx.createPattern(tmpCanvas, 'no-repeat');\n    ctx.fillRect(0, 0, x + width, y + height); // reassign mask\n\n    this.restoreStyles(element, ignoredStyles);\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\nMaskElement.ignoreStyles = ['mask', 'transform', 'clip-path'];\n\nvar noop = () => {// NOOP\n};\n\nclass ClipPathElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'clipPath';\n  }\n\n  apply(ctx) {\n    var {\n      document\n    } = this;\n    var contextProto = Reflect.getPrototypeOf(ctx);\n    var {\n      beginPath,\n      closePath\n    } = ctx;\n\n    if (contextProto) {\n      contextProto.beginPath = noop;\n      contextProto.closePath = noop;\n    }\n\n    Reflect.apply(beginPath, ctx, []);\n    this.children.forEach(child => {\n      if (typeof child.path === 'undefined') {\n        return;\n      }\n\n      var transform = typeof child.elementTransform !== 'undefined' ? child.elementTransform() : null; // handle <use />\n\n      if (!transform) {\n        transform = Transform.fromElement(document, child);\n      }\n\n      if (transform) {\n        transform.apply(ctx);\n      }\n\n      child.path(ctx);\n\n      if (contextProto) {\n        contextProto.closePath = closePath;\n      }\n\n      if (transform) {\n        transform.unapply(ctx);\n      }\n    });\n    Reflect.apply(closePath, ctx, []);\n    ctx.clip();\n\n    if (contextProto) {\n      contextProto.beginPath = beginPath;\n      contextProto.closePath = closePath;\n    }\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\n\nclass FilterElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'filter';\n  }\n\n  apply(ctx, element) {\n    // render as temp svg\n    var {\n      document,\n      children\n    } = this;\n    var boundingBox = element.getBoundingBox(ctx);\n\n    if (!boundingBox) {\n      return;\n    }\n\n    var px = 0;\n    var py = 0;\n    children.forEach(child => {\n      var efd = child.extraFilterDistance || 0;\n      px = Math.max(px, efd);\n      py = Math.max(py, efd);\n    });\n    var width = Math.floor(boundingBox.width);\n    var height = Math.floor(boundingBox.height);\n    var tmpCanvasWidth = width + 2 * px;\n    var tmpCanvasHeight = height + 2 * py;\n\n    if (tmpCanvasWidth < 1 || tmpCanvasHeight < 1) {\n      return;\n    }\n\n    var x = Math.floor(boundingBox.x);\n    var y = Math.floor(boundingBox.y);\n    var ignoredStyles = this.removeStyles(element, FilterElement.ignoreStyles);\n    var tmpCanvas = document.createCanvas(tmpCanvasWidth, tmpCanvasHeight);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    tmpCtx.translate(-x + px, -y + py);\n    element.render(tmpCtx); // apply filters\n\n    children.forEach(child => {\n      if (typeof child.apply === 'function') {\n        child.apply(tmpCtx, 0, 0, tmpCanvasWidth, tmpCanvasHeight);\n      }\n    }); // render on me\n\n    ctx.drawImage(tmpCanvas, 0, 0, tmpCanvasWidth, tmpCanvasHeight, x - px, y - py, tmpCanvasWidth, tmpCanvasHeight);\n    this.restoreStyles(element, ignoredStyles);\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\nFilterElement.ignoreStyles = ['filter', 'transform', 'clip-path'];\n\nclass FeDropShadowElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feDropShadow';\n    this.addStylesFromStyleDefinition();\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeMorphologyElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feMorphology';\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeCompositeElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feComposite';\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeGaussianBlurElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feGaussianBlur';\n    this.blurRadius = Math.floor(this.getAttribute('stdDeviation').getNumber());\n    this.extraFilterDistance = this.blurRadius;\n  }\n\n  apply(ctx, x, y, width, height) {\n    var {\n      document,\n      blurRadius\n    } = this;\n    var body = document.window ? document.window.document.body : null;\n    var canvas = ctx.canvas; // StackBlur requires canvas be on document\n\n    canvas.id = document.getUniqueId();\n\n    if (body) {\n      canvas.style.display = 'none';\n      body.appendChild(canvas);\n    }\n\n    (0,stackblur_canvas__WEBPACK_IMPORTED_MODULE_19__.canvasRGBA)(canvas, x, y, width, height, blurRadius);\n\n    if (body) {\n      body.removeChild(canvas);\n    }\n  }\n\n}\n\nclass TitleElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'title';\n  }\n\n}\n\nclass DescElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'desc';\n  }\n\n}\n\nvar elements = {\n  'svg': SVGElement,\n  'rect': RectElement,\n  'circle': CircleElement,\n  'ellipse': EllipseElement,\n  'line': LineElement,\n  'polyline': PolylineElement,\n  'polygon': PolygonElement,\n  'path': PathElement,\n  'pattern': PatternElement,\n  'marker': MarkerElement,\n  'defs': DefsElement,\n  'linearGradient': LinearGradientElement,\n  'radialGradient': RadialGradientElement,\n  'stop': StopElement,\n  'animate': AnimateElement,\n  'animateColor': AnimateColorElement,\n  'animateTransform': AnimateTransformElement,\n  'font': FontElement,\n  'font-face': FontFaceElement,\n  'missing-glyph': MissingGlyphElement,\n  'glyph': GlyphElement,\n  'text': TextElement,\n  'tspan': TSpanElement,\n  'tref': TRefElement,\n  'a': AElement,\n  'textPath': TextPathElement,\n  'image': ImageElement,\n  'g': GElement,\n  'symbol': SymbolElement,\n  'style': StyleElement,\n  'use': UseElement,\n  'mask': MaskElement,\n  'clipPath': ClipPathElement,\n  'filter': FilterElement,\n  'feDropShadow': FeDropShadowElement,\n  'feMorphology': FeMorphologyElement,\n  'feComposite': FeCompositeElement,\n  'feColorMatrix': FeColorMatrixElement,\n  'feGaussianBlur': FeGaussianBlurElement,\n  'title': TitleElement,\n  'desc': DescElement\n};\n\nfunction ownKeys$1(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$1(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys$1(Object(source), true).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys$1(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction createCanvas(width, height) {\n  var canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  return canvas;\n}\n\nfunction createImage(_x) {\n  return _createImage.apply(this, arguments);\n}\n\nfunction _createImage() {\n  _createImage = _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* (src) {\n    var anonymousCrossOrigin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var image = document.createElement('img');\n\n    if (anonymousCrossOrigin) {\n      image.crossOrigin = 'Anonymous';\n    }\n\n    return new Promise((resolve, reject) => {\n      image.onload = () => {\n        resolve(image);\n      };\n\n      image.onerror = (_event, _source, _lineno, _colno, error) => {\n        reject(error);\n      };\n\n      image.src = src;\n    });\n  });\n  return _createImage.apply(this, arguments);\n}\n\nclass Document {\n  constructor(canvg) {\n    var {\n      rootEmSize = 12,\n      emSize = 12,\n      createCanvas = Document.createCanvas,\n      createImage = Document.createImage,\n      anonymousCrossOrigin\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.canvg = canvg;\n    this.definitions = Object.create(null);\n    this.styles = Object.create(null);\n    this.stylesSpecificity = Object.create(null);\n    this.images = [];\n    this.fonts = [];\n    this.emSizeStack = [];\n    this.uniqueId = 0;\n    this.screen = canvg.screen;\n    this.rootEmSize = rootEmSize;\n    this.emSize = emSize;\n    this.createCanvas = createCanvas;\n    this.createImage = this.bindCreateImage(createImage, anonymousCrossOrigin);\n    this.screen.wait(this.isImagesLoaded.bind(this));\n    this.screen.wait(this.isFontsLoaded.bind(this));\n  }\n\n  bindCreateImage(createImage, anonymousCrossOrigin) {\n    if (typeof anonymousCrossOrigin === 'boolean') {\n      return (source, forceAnonymousCrossOrigin) => createImage(source, typeof forceAnonymousCrossOrigin === 'boolean' ? forceAnonymousCrossOrigin : anonymousCrossOrigin);\n    }\n\n    return createImage;\n  }\n\n  get window() {\n    return this.screen.window;\n  }\n\n  get fetch() {\n    return this.screen.fetch;\n  }\n\n  get ctx() {\n    return this.screen.ctx;\n  }\n\n  get emSize() {\n    var {\n      emSizeStack\n    } = this;\n    return emSizeStack[emSizeStack.length - 1];\n  }\n\n  set emSize(value) {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.push(value);\n  }\n\n  popEmSize() {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.pop();\n  }\n\n  getUniqueId() {\n    return \"canvg\".concat(++this.uniqueId);\n  }\n\n  isImagesLoaded() {\n    return this.images.every(_ => _.loaded);\n  }\n\n  isFontsLoaded() {\n    return this.fonts.every(_ => _.loaded);\n  }\n\n  createDocumentElement(document) {\n    var documentElement = this.createElement(document.documentElement);\n    documentElement.root = true;\n    documentElement.addStylesFromStyleDefinition();\n    this.documentElement = documentElement;\n    return documentElement;\n  }\n\n  createElement(node) {\n    var elementType = node.nodeName.replace(/^[^:]+:/, '');\n    var ElementType = Document.elementTypes[elementType];\n\n    if (typeof ElementType !== 'undefined') {\n      return new ElementType(this, node);\n    }\n\n    return new UnknownElement(this, node);\n  }\n\n  createTextNode(node) {\n    return new TextNode(this, node);\n  }\n\n  setViewBox(config) {\n    this.screen.setViewBox(_objectSpread$1({\n      document: this\n    }, config));\n  }\n\n}\nDocument.createCanvas = createCanvas;\nDocument.createImage = createImage;\nDocument.elementTypes = elements;\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n/**\r\n * SVG renderer on canvas.\r\n */\n\nclass Canvg {\n  /**\r\n   * Main constructor.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG Document.\r\n   * @param options - Rendering options.\r\n   */\n  constructor(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.parser = new Parser(options);\n    this.screen = new Screen(ctx, options);\n    this.options = options;\n    var document = new Document(this, options);\n    var documentElement = document.createDocumentElement(svg);\n    this.document = document;\n    this.documentElement = documentElement;\n  }\n  /**\r\n   * Create Canvg instance from SVG source string or URL.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  static from(ctx, svg) {\n    var _arguments = arguments;\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var options = _arguments.length > 2 && _arguments[2] !== undefined ? _arguments[2] : {};\n      var parser = new Parser(options);\n      var svgDocument = yield parser.parse(svg);\n      return new Canvg(ctx, svgDocument, options);\n    })();\n  }\n  /**\r\n   * Create Canvg instance from SVG source string.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  static fromString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var parser = new Parser(options);\n    var svgDocument = parser.parseFromString(svg);\n    return new Canvg(ctx, svgDocument, options);\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  fork(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.from(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  forkString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.fromString(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Document is ready promise.\r\n   * @returns Ready promise.\r\n   */\n\n\n  ready() {\n    return this.screen.ready();\n  }\n  /**\r\n   * Document is ready value.\r\n   * @returns Is ready or not.\r\n   */\n\n\n  isReady() {\n    return this.screen.isReady();\n  }\n  /**\r\n   * Render only first frame, ignoring animations and mouse.\r\n   * @param options - Rendering options.\r\n   */\n\n\n  render() {\n    var _arguments2 = arguments,\n        _this = this;\n\n    return _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__(function* () {\n      var options = _arguments2.length > 0 && _arguments2[0] !== undefined ? _arguments2[0] : {};\n\n      _this.start(_objectSpread({\n        enableRedraw: true,\n        ignoreAnimation: true,\n        ignoreMouse: true\n      }, options));\n\n      yield _this.ready();\n\n      _this.stop();\n    })();\n  }\n  /**\r\n   * Start rendering.\r\n   * @param options - Render options.\r\n   */\n\n\n  start() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var {\n      documentElement,\n      screen,\n      options: baseOptions\n    } = this;\n    screen.start(documentElement, _objectSpread(_objectSpread({\n      enableRedraw: true\n    }, baseOptions), options));\n  }\n  /**\r\n   * Stop rendering.\r\n   */\n\n\n  stop() {\n    this.screen.stop();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.documentElement.resize(width, height, preserveAspectRatio);\n  }\n\n}\n\n\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/canvg/lib/index.es.js\n");

/***/ })

};
;