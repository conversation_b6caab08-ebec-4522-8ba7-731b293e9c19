import { <PERSON><PERSON> } from "./ui/button";
import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import { ArrowRight, MapPin, TrendingUp, Award, Calendar } from "lucide-react";
import { ImageWithFallback } from "./figma/ImageWithFallback";

const featuredProjects = [
  {
    id: 1,
    name: "Marina Heights",
    location: "Dubai Marina",
    priceRange: "AED 2.5M - 8.5M",
    developer: "Emaar Properties",
    image:
      "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80",
    status: "Off-plan",
    completion: "Q4 2025",
    bedrooms: "1-3 BR",
    bathrooms: "2-4",
    size: "800-2,100 sq ft",
    roi: "8.5% ROI",
    features: ["Sea View", "Gym", "Pool", "Marina Walk"],
    paymentPlan: "20/80 Plan",
    handover: "Q4 2025",
  },
  {
    id: 2,
    name: "Downtown Residences",
    location: "Downtown Dubai",
    priceRange: "AED 1.8M - 6.2M",
    developer: "DAMAC Properties",
    image:
      "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80",
    status: "Ready",
    completion: "Completed",
    bedrooms: "Studio-2 BR",
    bathrooms: "1-3",
    size: "450-1,400 sq ft",
    roi: "7.2% ROI",
    features: ["Burj View", "Metro Link", "Mall Access", "Valet"],
    paymentPlan: "Ready to Move",
    handover: "Immediate",
  },
  {
    id: 3,
    name: "Palm Luxury Villas",
    location: "Palm Jumeirah",
    priceRange: "AED 12M - 25M",
    developer: "Nakheel",
    image:
      "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80",
    status: "Off-plan",
    completion: "Q2 2026",
    bedrooms: "4-6 BR",
    bathrooms: "5-7",
    size: "4,500-8,000 sq ft",
    roi: "6.8% ROI",
    features: ["Private Beach", "Infinity Pool", "Garden", "Staff Quarters"],
    paymentPlan: "40/60 Plan",
    handover: "Q2 2026",
  },
];

interface FeaturedProjectsProps {
  onProjectSelect?: (project: any) => void;
  onViewAllProjects?: () => void;
}

export function FeaturedProjects({
  onProjectSelect,
  onViewAllProjects,
}: FeaturedProjectsProps) {
  return (
    <section id="projects" className="section-padding bg-light-gray">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="mb-4 text-deep-blue text-[36px] text-[40px]">
            Featured Projects
          </h2>
          <p className="text-[rgba(0,0,0,1)] max-w-2xl mx-auto">
            Discover premium real estate opportunities from Dubai's most trusted
            developers
          </p>
        </div>

        {/* Projects Grid - Enhanced detailed cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredProjects.map((project) => (
            <Card
              key={project.id}
              className="overflow-hidden shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] hover:-translate-y-0.5 transition-all duration-300 rounded-lg border-0 h-full flex flex-col"
            >
              <div className="relative">
                <ImageWithFallback
                  src={project.image}
                  alt={project.name}
                  className="w-full h-56 object-cover"
                />
                <Badge
                  className={`absolute top-4 left-4 ${
                    project.status === "Ready"
                      ? "bg-emerald-green"
                      : project.status === "Off-plan"
                      ? "bg-deep-blue"
                      : "bg-orange-500"
                  }`}
                >
                  {project.status}
                </Badge>

                {/* ROI Badge */}
                <Badge className="absolute top-4 right-4 bg-gold text-soft-brown">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  {project.roi}
                </Badge>
              </div>

              <CardContent className="px-6 pt-4 pb-6 flex flex-col flex-1">
                <div className="flex flex-col flex-1">
                  {/* Property Name */}
                  <h4
                    className="text-lg font-medium mb-2 text-deep-blue truncate"
                    title={project.name}
                  >
                    {project.name}
                  </h4>

                  {/* Location */}
                  <div className="flex items-center text-muted-foreground mb-4">
                    <MapPin className="w-4 h-4 mr-2 flex-shrink-0" />
                    <span className="text-sm truncate text-[rgba(0,0,0,1)]">
                      {project.location}
                    </span>
                  </div>

                  {/* Price Range */}
                  <div className="mb-4">
                    <div className="text-xl font-medium text-deep-blue mb-1">
                      {project.priceRange}
                    </div>
                    <div className="text-sm text-[rgba(0,0,0,1)]">
                      {project.paymentPlan}
                    </div>
                  </div>

                  {/* Developer & Completion */}
                  <div className="space-y-2 mb-6 flex-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground flex items-center">
                        <Award className="w-3 h-3 mr-1" />
                        Developer
                      </span>
                      <span
                        className="text-sm truncate ml-2 text-[rgba(0,0,0,1)]"
                        title={project.developer}
                      >
                        {project.developer}
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        Handover
                      </span>
                      <span className="text-sm text-[rgba(0,0,0,1)]">
                        {project.handover}
                      </span>
                    </div>
                  </div>

                  {/* CTA Button - Always at bottom */}
                  <Button
                    className="w-full bg-deep-blue hover:bg-deep-blue/90 mt-auto"
                    onClick={() => onProjectSelect?.(project)}
                  >
                    View Details
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center mt-12">
          <Button
            variant="outline"
            size="lg"
            className="border-deep-blue text-deep-blue hover:bg-deep-blue hover:text-white"
            onClick={onViewAllProjects}
          >
            View All Projects
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
}
