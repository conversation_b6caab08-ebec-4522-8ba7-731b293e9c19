"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SplashScreen__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/SplashScreen */ \"(app-pages-browser)/./app/components/SplashScreen.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/Navbar */ \"(app-pages-browser)/./app/components/Navbar.tsx\");\n/* harmony import */ var _components_HeroSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/HeroSection */ \"(app-pages-browser)/./app/components/HeroSection.tsx\");\n/* harmony import */ var _components_FeaturedProjects__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/FeaturedProjects */ \"(app-pages-browser)/./app/components/FeaturedProjects.tsx\");\n/* harmony import */ var _components_PropertyFilters__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/PropertyFilters */ \"(app-pages-browser)/./app/components/PropertyFilters.tsx\");\n/* harmony import */ var _components_DevelopersListing__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/DevelopersListing */ \"(app-pages-browser)/./app/components/DevelopersListing.tsx\");\n/* harmony import */ var _components_PropertyListings__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/PropertyListings */ \"(app-pages-browser)/./app/components/PropertyListings.tsx\");\n/* harmony import */ var _components_MarketInfo__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/MarketInfo */ \"(app-pages-browser)/./app/components/MarketInfo.tsx\");\n/* harmony import */ var _components_AboutCompany__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/AboutCompany */ \"(app-pages-browser)/./app/components/AboutCompany.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./components/Testimonials */ \"(app-pages-browser)/./app/components/Testimonials.tsx\");\n/* harmony import */ var _components_ContactUs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./components/ContactUs */ \"(app-pages-browser)/./app/components/ContactUs.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    // Enhanced splash screen state management for smooth transitions\n    const [showSplash, setShowSplash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [splashExiting, setSplashExiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMainContentReady, setIsMainContentReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLogoClickSplash, setIsLogoClickSplash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contentVisible, setContentVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Session management for splash screen\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // Check if user has already seen splash screen in this session\n            const hasSeenSplash = sessionStorage.getItem(\"smart-off-plan-splash-seen\");\n            if (hasSeenSplash) {\n                // Skip splash screen for this session with immediate content display\n                setShowSplash(false);\n                setSplashExiting(false);\n                setIsMainContentReady(true);\n                setContentVisible(true);\n            } else {\n                // Pre-render main content but keep it hidden for smooth transition\n                setTimeout({\n                    \"HomePage.useEffect\": ()=>{\n                        setIsMainContentReady(true);\n                    }\n                }[\"HomePage.useEffect\"], 2000); // Pre-render before splash completes\n            }\n        }\n    }[\"HomePage.useEffect\"], []);\n    // Enhanced splash screen completion with perfect timing\n    const handleSplashComplete = ()=>{\n        // Mark splash as seen for this session (unless it's a logo click splash)\n        if (!isLogoClickSplash) {\n            sessionStorage.setItem(\"smart-off-plan-splash-seen\", \"true\");\n        }\n        // Start smooth exit sequence\n        setSplashExiting(true);\n        // If it was a logo click splash, reset navigation states\n        if (isLogoClickSplash) {\n            setIsLogoClickSplash(false);\n        }\n        // Coordinate splash exit with content entrance for seamless transition\n        setTimeout(()=>{\n            // Start content entrance while splash is still visible but fading\n            setContentVisible(true);\n            // Remove splash after content starts animating in\n            setTimeout(()=>{\n                setShowSplash(false);\n                setSplashExiting(false);\n                // Smooth scroll to top for logo click splashes\n                if (isLogoClickSplash) {\n                    window.scrollTo({\n                        top: 0,\n                        behavior: \"smooth\"\n                    });\n                }\n            }, 400); // Allow overlap for smooth transition\n        }, 600); // Start content transition before splash fully exits\n    };\n    // Enhanced logo click handler with proper state management\n    const handleLogoClick = ()=>{\n        // Set flag to indicate this is a logo click splash\n        setIsLogoClickSplash(true);\n        // Reset all transition states for fresh animation\n        setContentVisible(false);\n        setSplashExiting(false);\n        // Pre-render content for smooth transition\n        setIsMainContentReady(true);\n        // Show splash screen again\n        setShowSplash(true);\n    // Don't update session storage for logo clicks - cinematic experience every time\n    };\n    // Main home page content\n    const renderHomeContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-ivory\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_3__.Navbar, {\n                    onLogoClick: handleLogoClick,\n                    currentPage: \"home\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeroSection__WEBPACK_IMPORTED_MODULE_4__.HeroSection, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FeaturedProjects__WEBPACK_IMPORTED_MODULE_5__.FeaturedProjects, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PropertyFilters__WEBPACK_IMPORTED_MODULE_6__.PropertyFilters, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DevelopersListing__WEBPACK_IMPORTED_MODULE_7__.DevelopersListing, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PropertyListings__WEBPACK_IMPORTED_MODULE_8__.PropertyListings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarketInfo__WEBPACK_IMPORTED_MODULE_9__.MarketInfo, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AboutCompany__WEBPACK_IMPORTED_MODULE_10__.AboutCompany, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_11__.Testimonials, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContactUs__WEBPACK_IMPORTED_MODULE_12__.ContactUs, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"bg-soft-brown text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"section-padding\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"mb-6 text-white\",\n                                                    children: \"Smart Off Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-tan text-sm mb-6 leading-relaxed\",\n                                                    children: \"Your trusted partner for Dubai property investments. Connecting international investors with premium off-plan opportunities.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"f\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"t\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"in\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-gold hover:text-soft-brown transition-all duration-300 cursor-pointer\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"ig\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"mb-6 text-white\",\n                                                    children: \"Quick Links\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/\",\n                                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                                children: \"Home\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/properties\",\n                                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                                children: \"Projects\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/developers\",\n                                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                                children: \"Developers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/about\",\n                                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                                children: \"About Us\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/contact\",\n                                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                                children: \"Contact\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"mb-6 text-white\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/services/company-formation\",\n                                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                                children: \"Company Formation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/services/mortgages\",\n                                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                                children: \"Mortgages\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/services/golden-visa\",\n                                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                                children: \"Golden Visa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"mb-6 text-white\",\n                                                    children: \"Get In Touch\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-tan text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: \"\\uD83D\\uDCDE +971 4 123 4567\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: \"\\uD83D\\uDCE7 <EMAIL>\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: \"\\uD83D\\uDCCD Business Bay, Dubai, UAE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"text-white mb-3\",\n                                                                    children: \"Working Hours\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-tan text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: \"Mon - Fri: 9:00 AM - 7:00 PM\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 229,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: \"Sat: 10:00 AM - 4:00 PM\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 230,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-12 h-px bg-gradient-to-r from-transparent via-gold to-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-tan text-sm\",\n                                            children: \"\\xa9 2024 Smart Off Plan. All rights reserved.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-8 mt-4 md:mt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/privacy\",\n                                                    className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/terms\",\n                                                    className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/cookies\",\n                                                    className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                    children: \"Cookie Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n            lineNumber: 98,\n            columnNumber: 5\n        }, this);\n    async function fetchAreas() {\n        // Check if API configuration is available\n        if (!process.env.BASE_URL || !process.env.API_KEY) {\n            console.error(\"❌ Cannot fetch areas: API configuration missing\");\n            return null;\n        }\n        try {\n            console.log(\"🔄 Fetching areas from API...\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_13__[\"default\"].get(\"\".concat(process.env.BASE_URL, \"/api/areas\"), {\n                headers: {\n                    \"X-API-Key\": process.env.API_KEY\n                }\n            });\n            console.log(\"✅ Areas fetched successfully:\", response.data);\n            return response.data;\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error fetching areas:\");\n            console.error(\"Status:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status);\n            console.error(\"Data:\", (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data);\n            console.error(\"Message:\", error.message);\n            // Return null instead of throwing to prevent app crash\n            return null;\n        }\n    }\n    // Test API connection on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const testAPI = {\n                \"HomePage.useEffect.testAPI\": async ()=>{\n                    console.log(\"🧪 Testing API connection...\");\n                    const result = await fetchAreas();\n                    if (result) {\n                        console.log(\"✅ API test successful!\");\n                    } else {\n                        console.log(\"❌ API test failed!\");\n                    }\n                }\n            }[\"HomePage.useEffect.testAPI\"];\n            testAPI();\n        }\n    }[\"HomePage.useEffect\"], [\n        process.env.BASE_URL,\n        process.env.API_KEY\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"app-container\",\n        children: [\n            showSplash && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"splash-overlay \".concat(splashExiting ? \"splash-overlay-exiting\" : \"\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SplashScreen__WEBPACK_IMPORTED_MODULE_2__.SplashScreen, {\n                    onComplete: handleSplashComplete\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                lineNumber: 318,\n                columnNumber: 9\n            }, this),\n            isMainContentReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"app-content \".concat(!contentVisible ? \"app-content-hidden\" : contentVisible && !showSplash ? \"app-content-visible\" : \"app-content-entering\"),\n                children: renderHomeContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\page.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"cB0oW/MXM1qMkOp8eaogicFzjX0=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});