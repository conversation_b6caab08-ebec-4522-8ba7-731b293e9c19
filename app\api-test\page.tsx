'use client'

import { useState } from 'react';
import axios from 'axios';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { CheckCircle, XCircle, Loader2, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function APITestPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // API Configuration from environment variables
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
  const API_KEY = process.env.NEXT_PUBLIC_API_KEY;

  const testAPI = async () => {
    setIsLoading(true);
    setError(null);
    setTestResult(null);

    try {
      console.log('🧪 Testing API connection...');
      console.log('API Base URL:', API_BASE_URL);
      console.log('API Key:', API_KEY ? '***' + API_KEY.slice(-4) : 'Not set');

      if (!API_BASE_URL || !API_KEY) {
        throw new Error('API configuration missing. Please check your .env.local file.');
      }

      const response = await axios.get(`${API_BASE_URL}/api/areas`, {
        headers: {
          'X-API-Key': API_KEY,
        },
        timeout: 10000, // 10 second timeout
      });

      console.log('✅ API test successful:', response.data);
      setTestResult({
        success: true,
        status: response.status,
        data: response.data,
        headers: response.headers,
      });
    } catch (error: any) {
      console.error('❌ API test failed:', error);
      setError(error.response?.data?.message || error.message || 'Unknown error occurred');
      setTestResult({
        success: false,
        status: error.response?.status,
        data: error.response?.data,
        error: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-ivory p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <Link href="/" className="inline-flex items-center text-soft-brown hover:text-gold transition-colors mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>
          <h1 className="text-4xl font-bold text-soft-brown mb-2">API Test Page</h1>
          <p className="text-warm-gray">Test the Real Estate API connection and configuration</p>
        </div>

        {/* API Configuration Status */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Environment Configuration
              {API_BASE_URL && API_KEY ? (
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Configured
                </Badge>
              ) : (
                <Badge className="bg-red-100 text-red-800">
                  <XCircle className="w-3 h-3 mr-1" />
                  Missing
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">API Base URL:</span>
              <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                {API_BASE_URL || 'Not set'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">API Key:</span>
              <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                {API_KEY ? '***' + API_KEY.slice(-4) : 'Not set'}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Test Button */}
        <div className="mb-6">
          <Button 
            onClick={testAPI} 
            disabled={isLoading || !API_BASE_URL || !API_KEY}
            className="bg-soft-brown hover:bg-gold text-white"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Testing API...
              </>
            ) : (
              'Test API Connection'
            )}
          </Button>
        </div>

        {/* Results */}
        {testResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Test Results
                {testResult.success ? (
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Success
                  </Badge>
                ) : (
                  <Badge className="bg-red-100 text-red-800">
                    <XCircle className="w-3 h-3 mr-1" />
                    Failed
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <span className="font-medium">Status Code:</span>
                  <span className="ml-2 font-mono">{testResult.status || 'N/A'}</span>
                </div>
                
                {error && (
                  <div>
                    <span className="font-medium text-red-600">Error:</span>
                    <div className="mt-1 p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                      {error}
                    </div>
                  </div>
                )}

                {testResult.data && (
                  <div>
                    <span className="font-medium">Response Data:</span>
                    <pre className="mt-1 p-3 bg-gray-50 border rounded text-xs overflow-auto max-h-96">
                      {JSON.stringify(testResult.data, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Setup Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className="text-sm text-warm-gray">
              If the API test fails, please check the following:
            </p>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Ensure your <code className="bg-gray-100 px-1 rounded">.env.local</code> file exists in the project root</li>
              <li>Verify the environment variables are correctly set:
                <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
                  <li><code className="bg-gray-100 px-1 rounded">NEXT_PUBLIC_API_BASE_URL</code></li>
                  <li><code className="bg-gray-100 px-1 rounded">NEXT_PUBLIC_API_KEY</code></li>
                </ul>
              </li>
              <li>Restart the development server after making changes to environment variables</li>
              <li>Check the browser console for additional error details</li>
            </ol>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
