import { useState } from 'react';
import { <PERSON><PERSON> } from './ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { <PERSON>lider } from './ui/slider';
import { Badge } from './ui/badge';
import { ScrollArea } from './ui/scroll-area';
import { MapPin, Search, ZoomIn, ZoomOut, Layers, Maximize2, Heart, Bed, Bath, Square, Car, Filter, X } from 'lucide-react';
import { ImageWithFallback } from './figma/ImageWithFallback';

const mapProperties = [
  {
    id: 1,
    title: "Marina Heights",
    name: "Marina Heights", 
    location: "Dubai Marina",
    price: "From AED 2.5M",
    beds: 2,
    bedrooms: 2,
    baths: 2,
    bathrooms: 2,
    sqft: 1200,
    size: "1,200 sq ft",
    image: "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
    status: "Off-plan",
    coordinates: [25.0772, 55.1384],
    developer: "DAMAC Properties",
    completion: "Q4 2025",
    paymentPlan: "70/30",
    featured: true,
    roi: "8.5%",
    amenities: ["Pool", "Gym", "Parking", "Security"],
    rating: 4.8,
    type: "Apartment"
  },
  {
    id: 2,
    title: "Downtown Residences",
    name: "Downtown Residences",
    location: "Downtown Dubai", 
    price: "From AED 1.8M",
    beds: 1,
    bedrooms: 1,
    baths: 1,
    bathrooms: 1,
    sqft: 800,
    size: "800 sq ft",
    image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
    status: "Ready",
    coordinates: [25.1972, 55.2744],
    developer: "Emaar Properties",
    completion: "Ready",
    paymentPlan: "Cash",
    featured: false,
    roi: "7.2%",
    amenities: ["Pool", "Gym", "Metro Access", "Security"],
    rating: 4.7,
    type: "Apartment"
  },
  {
    id: 3,
    title: "Palm Villa",
    name: "Palm Villa",
    location: "Palm Jumeirah",
    price: "From AED 12M",
    beds: 4,
    bedrooms: 4,
    baths: 5,
    bathrooms: 5,
    sqft: 4500,
    size: "4,500 sq ft",
    image: "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
    status: "Under Construction",
    coordinates: [25.1124, 55.1390],
    developer: "Nakheel",
    completion: "Q3 2025",
    paymentPlan: "80/20",
    featured: true,
    roi: "6.8%",
    amenities: ["Private Beach", "Pool", "Garden", "Maid's Room"],
    rating: 4.9,
    type: "Villa"
  },
  {
    id: 4,
    title: "Business Tower",
    name: "Business Tower",
    location: "Business Bay",
    price: "From AED 950K",
    beds: 1,
    bedrooms: 1,
    baths: 1,
    bathrooms: 1,
    sqft: 650,
    size: "650 sq ft",
    image: "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
    status: "Ready",
    coordinates: [25.1870, 55.2631],
    developer: "Dubai Properties",
    completion: "Ready",
    paymentPlan: "Cash",
    featured: false,
    roi: "9.1%",
    amenities: ["Pool", "Gym", "Business Center", "Parking"],
    rating: 4.5,
    type: "Studio"
  },
  {
    id: 5,
    title: "Creek Residences",
    name: "Creek Residences",
    location: "Dubai Creek Harbour",
    price: "From AED 3.2M",
    beds: 3,
    bedrooms: 3,
    baths: 3,
    bathrooms: 3,
    sqft: 1800,
    size: "1,800 sq ft",
    image: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
    status: "Off-plan",
    coordinates: [25.1838, 55.3167],
    developer: "Emaar Properties",
    completion: "Q4 2026",
    paymentPlan: "70/30",
    featured: true,
    roi: "7.8%",
    amenities: ["Creek Views", "Pool", "Retail", "Metro Access"],
    rating: 4.6,
    type: "Apartment"
  }
];

interface PropertyFiltersProps {
  onPropertySelect?: (property: any) => void;
}

export function PropertyFilters({ onPropertySelect }: PropertyFiltersProps) {
  const [location, setLocation] = useState('');
  const [propertyType, setPropertyType] = useState('');
  const [selectedBedrooms, setSelectedBedrooms] = useState('');
  const [saleStatus, setSaleStatus] = useState('');
  const [area, setArea] = useState('');
  const [unitType, setUnitType] = useState('');
  const [devStatus, setDevStatus] = useState('');
  const [priceRange, setPriceRange] = useState([500000, 10000000]);
  const [showFilters, setShowFilters] = useState(false);

  const formatPrice = (price: number) => {
    if (price >= 1000000) {
      return `${(price / 1000000).toFixed(1)}M AED`;
    }
    return `${(price / 1000).toFixed(0)}K AED`;
  };

  const clearAllFilters = () => {
    setLocation('');
    setPropertyType('');
    setSelectedBedrooms('');
    setSaleStatus('');
    setArea('');
    setUnitType('');
    setDevStatus('');
    setPriceRange([500000, 10000000]);
  };

  const handlePropertyClick = (property: any) => {
    if (onPropertySelect) {
      onPropertySelect(property);
    }
  };

  const activeFiltersCount = [location, propertyType, selectedBedrooms, saleStatus, area, unitType, devStatus].filter(Boolean).length;

  return (
    <section id="map" className="section-padding bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="mb-4 text-deep-blue text-[36px] text-[40px]">Explore Properties</h2>
          <p className="text-[rgba(0,0,0,1)] max-w-2xl mx-auto">
            Use our advanced filters to find your perfect investment opportunity
          </p>
        </div>

        {/* Enhanced Filters Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="border-deep-blue text-deep-blue hover:bg-deep-blue hover:text-white"
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters {activeFiltersCount > 0 && `(${activeFiltersCount})`}
              </Button>
              
              {activeFiltersCount > 0 && (
                <Button
                  variant="ghost"
                  onClick={clearAllFilters}
                  className="text-muted-foreground hover:text-deep-blue"
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear All
                </Button>
              )}
            </div>

            <div className="text-sm text-[rgba(0,0,0,1)]">
              <span className="text-deep-blue">{mapProperties.length} properties</span> found
            </div>
          </div>

          {/* Horizontal Scrollable Filters */}
          {showFilters && (
            <Card className="border-gold/20 bg-gradient-to-r from-ivory to-beige/50">
              <CardContent className="p-6">
                <ScrollArea className="w-full">
                  <div className="flex gap-6 pb-4 min-w-max">
                    
                    {/* Sale Status */}
                    <div className="min-w-[200px]">
                      <label className="block text-sm mb-2 text-deep-blue">Sale Status</label>
                      <Select value={saleStatus} onValueChange={setSaleStatus}>
                        <SelectTrigger className="rounded-lg border-gold/30 bg-white/80 backdrop-blur-sm">
                          <SelectValue placeholder="Sale status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="for-sale">For Sale</SelectItem>
                          <SelectItem value="sold">Sold</SelectItem>
                          <SelectItem value="reserved">Reserved</SelectItem>
                          <SelectItem value="coming-soon">Coming Soon</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Price Range */}
                    <div className="min-w-[280px]">
                      <label className="block text-sm mb-4 text-deep-blue">
                        Price Range: {formatPrice(priceRange[0])} - {formatPrice(priceRange[1])}
                      </label>
                      <Slider
                        value={priceRange}
                        onValueChange={setPriceRange}
                        min={500000}
                        max={20000000}
                        step={100000}
                        className="w-full"
                      />
                    </div>

                    {/* Area */}
                    <div className="min-w-[200px]">
                      <label className="block text-sm mb-2 text-deep-blue">Area</label>
                      <Select value={area} onValueChange={setArea}>
                        <SelectTrigger className="rounded-lg border-gold/30 bg-white/80 backdrop-blur-sm">
                          <SelectValue placeholder="Select area" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="dubai-marina">Dubai Marina</SelectItem>
                          <SelectItem value="downtown">Downtown Dubai</SelectItem>
                          <SelectItem value="palm-jumeirah">Palm Jumeirah</SelectItem>
                          <SelectItem value="business-bay">Business Bay</SelectItem>
                          <SelectItem value="creek-harbour">Dubai Creek Harbour</SelectItem>
                          <SelectItem value="jbr">JBR</SelectItem>
                          <SelectItem value="dubai-hills">Dubai Hills</SelectItem>
                          <SelectItem value="city-walk">City Walk</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Unit Type */}
                    <div className="min-w-[200px]">
                      <label className="block text-sm mb-2 text-deep-blue">Unit Type</label>
                      <Select value={unitType} onValueChange={setUnitType}>
                        <SelectTrigger className="rounded-lg border-gold/30 bg-white/80 backdrop-blur-sm">
                          <SelectValue placeholder="Unit type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="studio">Studio</SelectItem>
                          <SelectItem value="1br">1 Bedroom</SelectItem>
                          <SelectItem value="2br">2 Bedroom</SelectItem>
                          <SelectItem value="3br">3 Bedroom</SelectItem>
                          <SelectItem value="4br">4+ Bedroom</SelectItem>
                          <SelectItem value="penthouse">Penthouse</SelectItem>
                          <SelectItem value="villa">Villa</SelectItem>
                          <SelectItem value="townhouse">Townhouse</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Development Status */}
                    <div className="min-w-[200px]">
                      <label className="block text-sm mb-2 text-deep-blue">Development Status</label>
                      <Select value={devStatus} onValueChange={setDevStatus}>
                        <SelectTrigger className="rounded-lg border-gold/30 bg-white/80 backdrop-blur-sm">
                          <SelectValue placeholder="Dev status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="off-plan">Off-Plan</SelectItem>
                          <SelectItem value="under-construction">Under Construction</SelectItem>
                          <SelectItem value="ready">Ready to Move</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Bedrooms */}
                    <div className="min-w-[200px]">
                      <label className="block text-sm mb-2 text-deep-blue">Bedrooms</label>
                      <Select value={selectedBedrooms} onValueChange={setSelectedBedrooms}>
                        <SelectTrigger className="rounded-lg border-gold/30 bg-white/80 backdrop-blur-sm">
                          <SelectValue placeholder="Bedrooms" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="studio">Studio</SelectItem>
                          <SelectItem value="1">1 BR</SelectItem>
                          <SelectItem value="2">2 BR</SelectItem>
                          <SelectItem value="3">3 BR</SelectItem>
                          <SelectItem value="4">4+ BR</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Property Type */}
                    <div className="min-w-[200px]">
                      <label className="block text-sm mb-2 text-deep-blue">Property Type</label>
                      <Select value={propertyType} onValueChange={setPropertyType}>
                        <SelectTrigger className="rounded-lg border-gold/30 bg-white/80 backdrop-blur-sm">
                          <SelectValue placeholder="Property type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="apartment">Apartment</SelectItem>
                          <SelectItem value="villa">Villa</SelectItem>
                          <SelectItem value="townhouse">Townhouse</SelectItem>
                          <SelectItem value="penthouse">Penthouse</SelectItem>
                          <SelectItem value="office">Office</SelectItem>
                          <SelectItem value="retail">Retail</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                  </div>
                </ScrollArea>

                {/* Search Button */}
                <div className="flex justify-center mt-6">
                  <Button className="bg-deep-blue hover:bg-deep-blue/90 px-8 py-2 text-white">
                    <Search className="w-4 h-4 mr-2" />
                    Search Properties
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Active Filters Display */}
          {activeFiltersCount > 0 && (
            <div className="flex flex-wrap gap-2 mt-4">
              {location && (
                <Badge variant="secondary" className="bg-gold/10 text-deep-blue border-gold/30">
                  Location: {location.replace('-', ' ')}
                  <X className="w-3 h-3 ml-1 cursor-pointer" onClick={() => setLocation('')} />
                </Badge>
              )}
              {saleStatus && (
                <Badge variant="secondary" className="bg-gold/10 text-deep-blue border-gold/30">
                  Status: {saleStatus.replace('-', ' ')}
                  <X className="w-3 h-3 ml-1 cursor-pointer" onClick={() => setSaleStatus('')} />
                </Badge>
              )}
              {area && (
                <Badge variant="secondary" className="bg-gold/10 text-deep-blue border-gold/30">
                  Area: {area.replace('-', ' ')}
                  <X className="w-3 h-3 ml-1 cursor-pointer" onClick={() => setArea('')} />
                </Badge>
              )}
              {unitType && (
                <Badge variant="secondary" className="bg-gold/10 text-deep-blue border-gold/30">
                  Unit: {unitType}
                  <X className="w-3 h-3 ml-1 cursor-pointer" onClick={() => setUnitType('')} />
                </Badge>
              )}
              {devStatus && (
                <Badge variant="secondary" className="bg-gold/10 text-deep-blue border-gold/30">
                  Dev: {devStatus.replace('-', ' ')}
                  <X className="w-3 h-3 ml-1 cursor-pointer" onClick={() => setDevStatus('')} />
                </Badge>
              )}
              {selectedBedrooms && (
                <Badge variant="secondary" className="bg-gold/10 text-deep-blue border-gold/30">
                  Beds: {selectedBedrooms}
                  <X className="w-3 h-3 ml-1 cursor-pointer" onClick={() => setSelectedBedrooms('')} />
                </Badge>
              )}
              {propertyType && (
                <Badge variant="secondary" className="bg-gold/10 text-deep-blue border-gold/30">
                  Type: {propertyType}
                  <X className="w-3 h-3 ml-1 cursor-pointer" onClick={() => setPropertyType('')} />
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Main Content Area */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          
          {/* Map Area */}
          <div className="lg:col-span-8 order-2 lg:order-1">
            <Card className="overflow-hidden h-[600px] shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]">
              <div className="relative h-full bg-gradient-to-br from-beige to-light-gold/20">
                
                {/* Map Placeholder */}
                <div className="w-full h-full relative overflow-hidden rounded-lg">
                  {/* Google Maps Embed */}
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d462560.6833123931!2d54.947368!3d25.276987!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e5f43496ad9c645%3A0xbde66e5084295162!2sDubai%20-%20United%20Arab%20Emirates!5e0!3m2!1sen!2s!4v1703123456789!5m2!1sen!2s"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    className="absolute inset-0"
                  />
                  
                  {/* Luxury Map Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-soft-brown/20 via-transparent to-transparent pointer-events-none" />
                  
                  {/* Property Location Markers - Positioned over Google Maps */}
                  <div className="absolute top-[30%] left-[25%] transform -translate-x-1/2 -translate-y-1/2 pointer-events-auto">
                    <div className="w-4 h-4 bg-gold border-2 border-white rounded-full shadow-lg pulse-animation relative cursor-pointer hover:scale-110 transition-transform">
                      <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-lg text-xs text-soft-brown whitespace-nowrap shadow-lg border border-gold/20 opacity-0 group-hover:opacity-100 transition-opacity">
                        Dubai Marina
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-white/95"></div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="absolute top-[45%] right-[30%] transform translate-x-1/2 -translate-y-1/2 pointer-events-auto">
                    <div className="w-4 h-4 bg-gold border-2 border-white rounded-full shadow-lg pulse-animation relative cursor-pointer hover:scale-110 transition-transform">
                      <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-lg text-xs text-soft-brown whitespace-nowrap shadow-lg border border-gold/20 opacity-0 group-hover:opacity-100 transition-opacity">
                        Downtown Dubai
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-white/95"></div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="absolute top-[60%] left-[20%] transform -translate-x-1/2 -translate-y-1/2 pointer-events-auto">
                    <div className="w-4 h-4 bg-gold border-2 border-white rounded-full shadow-lg pulse-animation relative cursor-pointer hover:scale-110 transition-transform">
                      <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-lg text-xs text-soft-brown whitespace-nowrap shadow-lg border border-gold/20 opacity-0 group-hover:opacity-100 transition-opacity">
                        Palm Jumeirah
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-white/95"></div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="absolute top-[40%] right-[25%] transform translate-x-1/2 -translate-y-1/2 pointer-events-auto">
                    <div className="w-4 h-4 bg-gold border-2 border-white rounded-full shadow-lg pulse-animation relative cursor-pointer hover:scale-110 transition-transform">
                      <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-lg text-xs text-soft-brown whitespace-nowrap shadow-lg border border-gold/20 opacity-0 group-hover:opacity-100 transition-opacity">
                        Business Bay
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-white/95"></div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="absolute top-[35%] left-[40%] transform -translate-x-1/2 -translate-y-1/2 pointer-events-auto">
                    <div className="w-4 h-4 bg-gold border-2 border-white rounded-full shadow-lg pulse-animation relative cursor-pointer hover:scale-110 transition-transform">
                      <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-lg text-xs text-soft-brown whitespace-nowrap shadow-lg border border-gold/20 opacity-0 group-hover:opacity-100 transition-opacity">
                        Dubai Creek Harbour
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-white/95"></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Enhanced Map Legend */}
                  <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg p-4 shadow-lg border border-gold/20 pointer-events-auto">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-3 h-3 bg-gold border border-white rounded-full shadow-sm"></div>
                      <span className="text-xs text-soft-brown">Premium Properties</span>
                    </div>
                    <div className="text-xs text-warm-gray mb-2">Live Google Maps View</div>
                    <div className="text-xs text-soft-brown opacity-75">Click markers for details</div>
                  </div>
                  
                  {/* Custom Map Controls */}
                  <div className="absolute top-4 right-4 flex flex-col space-y-2 pointer-events-auto">
                    <button className="w-10 h-10 bg-white/95 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg hover:bg-white hover:shadow-xl transition-all duration-300 border border-gold/20">
                      <Layers className="w-4 h-4 text-soft-brown" />
                    </button>
                    <button className="w-10 h-10 bg-white/95 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg hover:bg-white hover:shadow-xl transition-all duration-300 border border-gold/20">
                      <Maximize2 className="w-4 h-4 text-soft-brown" />
                    </button>
                  </div>
                  
                  {/* Smart Off Plan Branding Overlay */}
                  <div className="absolute top-4 left-4 pointer-events-auto">
                    <div className="bg-white/95 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-gold/20">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-gold rounded-full"></div>
                        <span className="text-xs text-soft-brown">Smart Off Plan</span>
                      </div>
                      <div className="text-xs text-warm-gray mt-1">Dubai Properties</div>
                    </div>
                  </div>
                </div>

                {/* Map Controls */}
                <div className="absolute top-4 right-4 space-y-2">
                  <Button variant="outline" size="sm" className="w-10 h-10 p-0 bg-white/90 backdrop-blur-sm">
                    <ZoomIn className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="w-10 h-10 p-0 bg-white/90 backdrop-blur-sm">
                    <ZoomOut className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="w-10 h-10 p-0 bg-white/90 backdrop-blur-sm">
                    <Layers className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="w-10 h-10 p-0 bg-white/90 backdrop-blur-sm">
                    <Maximize2 className="w-4 h-4" />
                  </Button>
                </div>

                {/* Property Price Markers - Floating over Google Maps */}
                {mapProperties.map((property, index) => {
                  const positions = [
                    { top: '25%', left: '30%' },
                    { top: '40%', right: '35%' },
                    { top: '55%', left: '25%' },
                    { top: '35%', right: '30%' },
                    { top: '30%', left: '45%' }
                  ];
                  const position = positions[index] || positions[0];
                  
                  return (
                    <div
                      key={property.id}
                      className="absolute pointer-events-auto z-10 group"
                      style={position}
                    >
                      <div 
                        className="bg-soft-brown text-white rounded-lg px-3 py-2 shadow-lg cursor-pointer hover:bg-gold hover:scale-105 transition-all duration-300 text-sm border border-white/20"
                        onClick={() => handlePropertyClick(property)}
                      >
                        <div className="flex items-center space-x-2">
                          <MapPin className="w-3 h-3" />
                          <span>{property.price}</span>
                        </div>
                        
                        {/* Hover Card */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="bg-white rounded-lg p-3 shadow-xl border border-gold/20 w-48">
                            <h5 className="text-soft-brown text-sm mb-1">{property.name}</h5>
                            <p className="text-warm-gray text-xs mb-2">{property.location}</p>
                            <div className="flex items-center justify-between text-xs">
                              <span className="text-soft-brown">{property.beds} bed, {property.baths} bath</span>
                              <Badge className={`text-xs ${
                                property.status === 'Ready'
                                  ? 'bg-emerald-green'
                                  : property.status === 'Off-plan'
                                  ? 'bg-deep-blue'
                                  : 'bg-orange-500'
                              }`}>
                                {property.status}
                              </Badge>
                            </div>
                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </Card>
          </div>

          {/* Property Results */}
          <div className="lg:col-span-4 order-1 lg:order-2">
            <Card className="h-[600px] shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]">
              <CardHeader>
                <CardTitle className="text-deep-blue flex items-center justify-between">
                  Properties
                  <Badge variant="secondary" className="bg-gold/10 text-deep-blue">
                    {mapProperties.length}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <ScrollArea className="h-[500px] px-6">
                  <div className="space-y-4">
                    {mapProperties.map((property) => (
                      <div
                        key={property.id}
                        className="border border-border rounded-lg p-4 hover:border-gold/50 hover:shadow-md transition-all duration-300 cursor-pointer"
                        onClick={() => handlePropertyClick(property)}
                      >
                        <div className="flex gap-4">
                          <ImageWithFallback
                            src={property.image}
                            alt={property.name}
                            className="w-20 h-20 object-cover rounded-lg"
                          />
                          <div className="flex-1 min-w-0">
                            <h4 className="text-deep-blue mb-1 truncate">{property.name}</h4>
                            <div className="flex items-center text-muted-foreground text-sm mb-2">
                              <MapPin className="w-3 h-3 mr-1" />
                              {property.location}
                            </div>
                            <div className="text-deep-blue mb-2">{property.price}</div>
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <div className="flex items-center">
                                <Bed className="w-3 h-3 mr-1" />
                                {property.beds}
                              </div>
                              <div className="flex items-center">
                                <Bath className="w-3 h-3 mr-1" />
                                {property.baths}
                              </div>
                              <div className="flex items-center">
                                <Square className="w-3 h-3 mr-1" />
                                {property.sqft} sqft
                              </div>
                            </div>
                            <Badge
                              className={`mt-2 text-xs ${
                                property.status === 'Ready'
                                  ? 'bg-emerald-green'
                                  : property.status === 'Off-plan'
                                  ? 'bg-deep-blue'
                                  : 'bg-orange-500'
                              }`}
                            >
                              {property.status}
                            </Badge>
                          </div>
                          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-gold">
                            <Heart className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </div>

      </div>
    </section>
  );
}