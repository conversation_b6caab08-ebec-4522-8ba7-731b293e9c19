import type { Metadata } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";

// Configure fonts
const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

const playfairDisplay = Playfair_Display({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-playfair",
});

export const metadata: Metadata = {
  title: "Smart Off Plan - Dubai Property Investment",
  description:
    "Your trusted partner for Dubai property investments. Connecting international investors with premium off-plan opportunities.",
  keywords:
    "Dubai property, off-plan investment, real estate, property management, golden visa",
  authors: [{ name: "Smart Off Plan" }],
  openGraph: {
    title: "Smart Off Plan - Dubai Property Investment",
    description:
      "Your trusted partner for Dubai property investments. Connecting international investors with premium off-plan opportunities.",
    type: "website",
    locale: "en_US",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} ${playfairDisplay.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
