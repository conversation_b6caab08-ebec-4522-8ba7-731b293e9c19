import { useState } from 'react';
import { Card, CardContent } from './ui/card';
import { Button } from './ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { TrendingUp, Users, Building, Globe, ArrowRight, MapPin, Download, FileText, BarChart3, Calendar, DollarSign, Target } from 'lucide-react';
import { ImageWithFallback } from './figma/ImageWithFallback';

const marketInsights = [
  {
    icon: TrendingUp,
    title: "Rising Demand",
    description: "Property values in Dubai have increased by 15% year-over-year, with Dubai Marina and Downtown leading the growth.",
    stat: "+15% YoY",
    color: "text-soft-brown"
  },
  {
    icon: Users,
    title: "International Investment",
    description: "Over 70% of property purchases are made by international investors, particularly from Asia and Europe.",
    stat: "70% International",
    color: "text-soft-brown"
  },
  {
    icon: Building,
    title: "New Developments",
    description: "Dubai has 200+ new projects launching in 2024-2025, offering diverse investment opportunities.",
    stat: "200+ Projects",
    color: "text-soft-brown"
  },
  {
    icon: Globe,
    title: "Global Hub",
    description: "Dubai's strategic location and business-friendly policies make it a premier destination for property investment.",
    stat: "#1 Choice",
    color: "text-soft-brown"
  }
];

const topAreas = [
  { 
    area: "Dubai Marina", 
    growth: "+18%", 
    avgPrice: "AED 1.8M",
    image: "https://images.unsplash.com/photo-1512453979798-5ea266f8880c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    description: "Waterfront luxury living",
    gradient: "from-blue-900/70 via-blue-800/50 to-transparent"
  },
  { 
    area: "Downtown Dubai", 
    growth: "+15%", 
    avgPrice: "AED 2.1M",
    image: "https://images.unsplash.com/photo-1518684079-3c830dcef090?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    description: "Heart of the city",
    gradient: "from-gray-900/70 via-gray-700/50 to-transparent"
  },
  { 
    area: "Palm Jumeirah", 
    growth: "+22%", 
    avgPrice: "AED 8.5M",
    image: "https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    description: "Iconic palm paradise",
    gradient: "from-emerald-900/70 via-emerald-700/50 to-transparent"
  },
  { 
    area: "Business Bay", 
    growth: "+12%", 
    avgPrice: "AED 1.4M",
    image: "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    description: "Commercial excellence",
    gradient: "from-purple-900/70 via-purple-700/50 to-transparent"
  },
  { 
    area: "Dubai Hills", 
    growth: "+20%", 
    avgPrice: "AED 2.8M",
    image: "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    description: "Modern suburban elegance",
    gradient: "from-amber-900/70 via-amber-700/50 to-transparent"
  }
];

const marketReportData = [
  {
    icon: BarChart3,
    title: "Market Performance",
    value: "+18.5%",
    description: "Price appreciation YoY"
  },
  {
    icon: DollarSign,
    title: "Transaction Volume",
    value: "AED 285B",
    description: "Total market value"
  },
  {
    icon: Target,
    title: "Investment Opportunities",
    value: "320+",
    description: "Active off-plan projects"
  },
  {
    icon: TrendingUp,
    title: "ROI Forecast",
    value: "12-15%",
    description: "Expected annual returns"
  }
];

const detailedInsights = [
  {
    area: "Dubai Marina",
    priceRange: "AED 900K - AED 4.2M",
    growth: "+18%",
    roi: "8.5%",
    highlights: ["Waterfront location", "High rental demand", "Tourism proximity"]
  },
  {
    area: "Downtown Dubai",
    priceRange: "AED 1.2M - AED 8.5M",
    growth: "+15%",
    roi: "7.2%",
    highlights: ["Business district", "Burj Khalifa views", "Metro connectivity"]
  },
  {
    area: "Dubai Hills",
    priceRange: "AED 1.8M - AED 12M",
    growth: "+20%",
    roi: "9.1%",
    highlights: ["Family-friendly", "Golf course access", "Premium amenities"]
  },
  {
    area: "Business Bay",
    priceRange: "AED 750K - AED 3.8M",
    growth: "+12%",
    roi: "10.2%",
    highlights: ["Commercial hub", "Canal views", "Investment potential"]
  }
];

export function MarketInfo() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const generatePDF = async () => {
    // Dynamic import to avoid bundling issues
    const { jsPDF } = await import('jspdf');
    
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    let currentY = 20;

    // Colors (RGB values for jsPDF)
    const goldColor = [212, 175, 55];
    const softBrownColor = [139, 115, 85];
    const warmGrayColor = [138, 121, 104];

    // Helper function to add a new page if needed
    const checkAddPage = (requiredSpace: number) => {
      if (currentY + requiredSpace > pageHeight - 20) {
        doc.addPage();
        currentY = 20;
        return true;
      }
      return false;
    };

    // Header with company branding
    doc.setFillColor(...goldColor);
    doc.rect(0, 0, pageWidth, 40, 'F');
    
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(24);
    doc.setFont('helvetica', 'bold');
    doc.text('Smart Off Plan', 20, 18);
    
    doc.setFontSize(16);
    doc.setFont('helvetica', 'normal');
    doc.text('Dubai Market Report Q4 2024', 20, 28);
    
    doc.setFontSize(12);
    doc.text('Comprehensive Market Analysis and Investment Insights', 20, 35);

    currentY = 55;

    // Market Overview Section
    doc.setTextColor(...softBrownColor);
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text('Market Overview', 20, currentY);
    currentY += 15;

    // Market metrics in a grid
    const metricsPerRow = 2;
    marketReportData.forEach((metric, index) => {
      const col = index % metricsPerRow;
      const row = Math.floor(index / metricsPerRow);
      
      if (col === 0 && row > 0) {
        currentY += 25;
        checkAddPage(25);
      }

      const x = 20 + (col * (pageWidth - 40) / metricsPerRow);
      const boxWidth = (pageWidth - 60) / metricsPerRow;

      // Metric box background
      doc.setFillColor(245, 241, 235); // Beige background
      doc.rect(x, currentY - 5, boxWidth, 20, 'F');
      
      // Metric value
      doc.setTextColor(...goldColor);
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text(metric.value, x + 5, currentY + 5);
      
      // Metric title
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.text(metric.title, x + 5, currentY + 12);
      
      // Metric description
      doc.setTextColor(...warmGrayColor);
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(metric.description, x + 5, currentY + 18);
    });

    currentY += 40;
    checkAddPage(50);

    // Q4 2024 Market Summary
    doc.setTextColor(...softBrownColor);
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text('Q4 2024 Market Summary', 20, currentY);
    currentY += 20;

    // Key Highlights
    doc.setFontSize(14);
    doc.text('Key Highlights', 20, currentY);
    currentY += 10;

    const highlights = [
      'Dubai property prices increased by 18.5% year-over-year',
      'International investor participation reached 78%',
      'Off-plan sales contributed 65% of total transactions',
      'Luxury segment shows strongest growth at 25%'
    ];

    doc.setTextColor(...warmGrayColor);
    doc.setFontSize(11);
    doc.setFont('helvetica', 'normal');

    highlights.forEach((highlight) => {
      checkAddPage(8);
      doc.text('• ' + highlight, 25, currentY);
      currentY += 8;
    });

    currentY += 10;
    checkAddPage(40);

    // Investment Outlook
    doc.setTextColor(...softBrownColor);
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Investment Outlook', 20, currentY);
    currentY += 10;

    doc.setTextColor(...warmGrayColor);
    doc.setFontSize(11);
    doc.setFont('helvetica', 'normal');
    
    const outlookText = [
      'The Dubai property market continues to demonstrate exceptional resilience and',
      'growth potential. Key infrastructure projects, including the Dubai 2040 Urban',
      'Master Plan and Expo legacy developments, are driving sustained demand.',
      '',
      'Our analysis indicates continued appreciation of 12-15% annually through 2025,',
      'with particular strength in waterfront and master-planned communities.'
    ];

    outlookText.forEach((line) => {
      if (line === '') {
        currentY += 4;
      } else {
        checkAddPage(8);
        doc.text(line, 20, currentY);
        currentY += 6;
      }
    });

    currentY += 15;
    checkAddPage(30);

    // Area Performance Analysis
    doc.setTextColor(...softBrownColor);
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text('Area Performance Analysis', 20, currentY);
    currentY += 20;

    detailedInsights.forEach((area, index) => {
      checkAddPage(45);
      
      // Area header
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text(area.area, 20, currentY);
      
      // Growth badge
      doc.setTextColor(...goldColor);
      doc.setFontSize(12);
      doc.text(area.growth, pageWidth - 40, currentY);
      
      currentY += 8;

      // Price range and ROI
      doc.setTextColor(...warmGrayColor);
      doc.setFontSize(11);
      doc.setFont('helvetica', 'normal');
      doc.text('Price Range: ' + area.priceRange, 25, currentY);
      currentY += 6;
      doc.text('Average ROI: ' + area.roi, 25, currentY);
      currentY += 8;

      // Highlights
      doc.setFontSize(10);
      doc.text('Key Highlights:', 25, currentY);
      currentY += 6;
      
      area.highlights.forEach((highlight) => {
        doc.text('  • ' + highlight, 30, currentY);
        currentY += 5;
      });
      
      currentY += 10;
    });

    checkAddPage(80);

    // Why Invest in Dubai section
    doc.setTextColor(...softBrownColor);
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text('Why Invest in Dubai Real Estate?', 20, currentY);
    currentY += 20;

    const investmentBenefits = [
      { title: '0% Property Tax', description: 'No annual property taxes on real estate investments' },
      { title: '100% Foreign Ownership', description: 'Full ownership rights for international investors' },
      { title: '8-12% Rental Yields', description: 'Attractive annual rental returns' },
      { title: '15% Capital Growth', description: 'Strong appreciation potential' }
    ];

    investmentBenefits.forEach((benefit, index) => {
      const col = index % 2;
      const row = Math.floor(index / 2);
      
      if (col === 0 && row > 0) {
        currentY += 20;
        checkAddPage(20);
      }

      const x = 20 + (col * (pageWidth - 40) / 2);
      
      doc.setTextColor(...goldColor);
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text(benefit.title, x, currentY);
      
      doc.setTextColor(...warmGrayColor);
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      const lines = doc.splitTextToSize(benefit.description, (pageWidth - 60) / 2);
      doc.text(lines, x, currentY + 8);
    });

    currentY += 40;
    checkAddPage(50);

    // Footer
    doc.setFillColor(...softBrownColor);
    doc.rect(0, pageHeight - 30, pageWidth, 30, 'F');
    
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Smart Off Plan', 20, pageHeight - 18);
    
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text('Business Bay, Dubai, UAE | +971 4 123 4567 | <EMAIL>', 20, pageHeight - 10);
    
    doc.setTextColor(255, 255, 255);
    doc.text('Generated on: ' + new Date().toLocaleDateString(), pageWidth - 80, pageHeight - 10);

    // Save the PDF
    doc.save('Dubai-Market-Report-Q4-2024.pdf');
  };

  const handleDownloadReport = () => {
    generatePDF().catch((error) => {
      console.error('Error generating PDF:', error);
      // Fallback to simple download if PDF generation fails
      const link = document.createElement('a');
      link.href = 'data:text/plain;charset=utf-8,Dubai Market Report Q4 2024 - Please contact us for the full <NAME_EMAIL>';
      link.download = 'Dubai-Market-Report-Q4-2024.txt';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  };

  return (
    <section className="section-padding bg-ivory">
      <div className="container">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="mb-6 text-soft-brown text-[36px] text-[40px]">Dubai Property Market Insights</h2>
          <p className="text-warm-gray max-w-3xl mx-auto text-xl leading-relaxed">
            Stay informed with the latest market trends and insights from Dubai's dynamic real estate sector
          </p>
        </div>

        {/* Market Insights Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {marketInsights.map((insight, index) => {
            const IconComponent = insight.icon;
            return (
              <Card key={index} className="text-center rounded-2xl shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] transition-all duration-300 hover:-translate-y-1 border-0 group bg-white h-full">
                <CardContent className="p-8 h-full flex flex-col">
                  
                  {/* Icon */}
                  <div className="mb-6 flex justify-center">
                    <div className="w-16 h-16 bg-beige rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                      <IconComponent className={`w-8 h-8 ${insight.color}`} />
                    </div>
                  </div>

                  {/* Stat */}
                  <div className={`${insight.color} text-2xl text-center mb-6`}>
                    {insight.stat}
                  </div>

                  {/* Title */}
                  <h3 className="mb-4 group-hover:text-gold transition-colors text-[rgba(0,0,0,1)] text-center text-[16px]">
                    {insight.title}
                  </h3>
                  
                  {/* Description */}
                  <p className="text-[rgba(0,0,0,1)] text-sm leading-relaxed text-center flex-grow">
                    {insight.description}
                  </p>

                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Market Report CTA */}
        <div className="bg-white rounded-2xl p-12 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] mb-20">
          <div className="text-center">
            <h3 className="mb-6 text-soft-brown">Get Detailed Market Analysis</h3>
            <p className="text-[rgba(0,0,0,1)] mb-8 max-w-2xl mx-auto text-lg leading-relaxed">
              Download our comprehensive quarterly market report with in-depth analysis, 
              price trends, and investment recommendations for Dubai's property market.
            </p>
            
            {/* Market Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
              <div className="group">
                <div className="text-3xl text-soft-brown mb-3 group-hover:text-gold transition-colors">AED 420B</div>
                <p className="text-[rgba(0,0,0,1)]">Total Market Value</p>
              </div>
              <div className="group">
                <div className="text-3xl text-soft-brown mb-3 group-hover:text-gold transition-colors">85,000</div>
                <p className="text-[rgba(0,0,0,1)]">Transactions in 2024</p>
              </div>
              <div className="group">
                <div className="text-3xl text-soft-brown mb-3 group-hover:text-gold transition-colors">12.5%</div>
                <p className="text-[rgba(0,0,0,1)]">Average ROI</p>
              </div>
            </div>

            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
              <DialogTrigger asChild>
                <Button size="lg" className="bg-gold hover:bg-soft-brown text-[rgba(16,16,16,1)] hover:text-white px-8 py-4 rounded-xl transition-all duration-300">
                  Explore Market Reports
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-5xl w-[92vw] max-h-[90vh] overflow-hidden bg-white rounded-3xl border-0 p-0 shadow-[0_32px_64px_-12px_rgba(139,115,85,0.25)]">
                
                {/* Header with proper dialog structure */}
                <DialogHeader className="bg-gradient-to-r from-soft-brown to-gold p-8 text-white">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                        <FileText className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <DialogTitle className="text-3xl text-white mb-1">Dubai Market Report Q4 2024</DialogTitle>
                        <DialogDescription className="text-white/90 text-lg">
                          Comprehensive market analysis and investment insights for Dubai's real estate market including price trends, area performance, and investment opportunities
                        </DialogDescription>
                      </div>
                    </div>
                    <Button 
                      onClick={handleDownloadReport}
                      className="bg-white text-soft-brown hover:bg-white/90 rounded-xl px-6 py-3 shadow-lg"
                    >
                      <Download className="w-5 h-5 mr-2" />
                      Download PDF
                    </Button>
                  </div>
                </DialogHeader>

                {/* Scrollable Content */}
                <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
                  <div className="p-8 space-y-8">
                    
                    {/* Key Metrics */}
                    <div>
                      <h3 className="text-2xl text-soft-brown mb-6 flex items-center">
                        <BarChart3 className="w-6 h-6 mr-3 text-gold" />
                        Market Overview
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {marketReportData.map((item, index) => (
                          <div key={index} className="bg-gradient-to-br from-beige to-white rounded-xl p-6 text-center border border-tan/20">
                            <div className="w-12 h-12 bg-gold rounded-lg flex items-center justify-center mx-auto mb-4">
                              <item.icon className="w-6 h-6 text-white" />
                            </div>
                            <div className="text-2xl text-soft-brown mb-2">{item.value}</div>
                            <h4 className="text-soft-brown mb-1 text-sm">{item.title}</h4>
                            <p className="text-warm-gray text-xs">{item.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Market Summary */}
                    <div className="bg-beige rounded-2xl p-8">
                      <h3 className="text-2xl text-soft-brown mb-6 flex items-center">
                        <Calendar className="w-6 h-6 mr-3 text-gold" />
                        Q4 2024 Market Summary
                      </h3>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                          <h4 className="text-lg text-soft-brown mb-4">Key Highlights</h4>
                          <div className="space-y-3">
                            <div className="flex items-start">
                              <div className="w-2 h-2 bg-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                              <span className="text-warm-gray text-sm">Dubai property prices increased by 18.5% year-over-year</span>
                            </div>
                            <div className="flex items-start">
                              <div className="w-2 h-2 bg-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                              <span className="text-warm-gray text-sm">International investor participation reached 78%</span>
                            </div>
                            <div className="flex items-start">
                              <div className="w-2 h-2 bg-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                              <span className="text-warm-gray text-sm">Off-plan sales contributed 65% of total transactions</span>
                            </div>
                            <div className="flex items-start">
                              <div className="w-2 h-2 bg-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                              <span className="text-warm-gray text-sm">Luxury segment shows strongest growth at 25%</span>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h4 className="text-lg text-soft-brown mb-4">Investment Outlook</h4>
                          <p className="text-warm-gray text-sm leading-relaxed mb-4">
                            The Dubai property market continues to demonstrate exceptional resilience and growth potential. 
                            Key infrastructure projects, including the Dubai 2040 Urban Master Plan and Expo legacy developments, 
                            are driving sustained demand.
                          </p>
                          <p className="text-warm-gray text-sm leading-relaxed">
                            Our analysis indicates continued appreciation of 12-15% annually through 2025, with particular 
                            strength in waterfront and master-planned communities.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Area Performance */}
                    <div>
                      <h3 className="text-2xl text-soft-brown mb-6 flex items-center">
                        <MapPin className="w-6 h-6 mr-3 text-gold" />
                        Area Performance Analysis
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {detailedInsights.map((area, index) => (
                          <div key={index} className="bg-white rounded-xl border border-tan/20 p-6 hover:shadow-lg transition-shadow">
                            <div className="flex justify-between items-start mb-4">
                              <h4 className="text-lg text-soft-brown">{area.area}</h4>
                              <span className="text-gold bg-light-gold px-3 py-1 rounded-lg text-sm">{area.growth}</span>
                            </div>
                            <div className="space-y-2 mb-4">
                              <div className="flex justify-between text-sm">
                                <span className="text-warm-gray">Price Range:</span>
                                <span className="text-soft-brown">{area.priceRange}</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-warm-gray">Average ROI:</span>
                                <span className="text-soft-brown">{area.roi}</span>
                              </div>
                            </div>
                            <div>
                              <span className="text-warm-gray text-xs mb-2 block">Key Highlights:</span>
                              <div className="space-y-1">
                                {area.highlights.map((highlight, idx) => (
                                  <div key={idx} className="flex items-center text-xs text-warm-gray">
                                    <div className="w-1 h-1 bg-gold rounded-full mr-2"></div>
                                    {highlight}
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Call to Action */}
                    <div className="bg-gradient-to-r from-soft-brown to-gold rounded-2xl p-8 text-center text-white">
                      <h3 className="text-2xl text-white mb-4">Ready to Invest?</h3>
                      <p className="text-white/90 mb-6 max-w-2xl mx-auto">
                        Our expert team can help you identify the best investment opportunities based on this market analysis. 
                        Get personalized recommendations tailored to your investment goals.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button className="bg-white text-soft-brown hover:bg-white/90 px-8 py-3">
                          Schedule Consultation
                        </Button>
                        <Button variant="outline" className="border-white text-white hover:bg-white/10 px-8 py-3">
                          View Properties
                        </Button>
                      </div>
                    </div>

                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Top Performing Areas - Redesigned with Background Images */}
        <div>
          <div className="text-center mb-12">
            <h3 className="mb-6 text-soft-brown">Top Performing Investment Areas</h3>
            <p className="text-[rgba(0,0,0,1)] text-xl max-w-3xl mx-auto leading-relaxed">
              Explore Dubai's most promising neighborhoods with exceptional growth potential and luxury amenities
            </p>
          </div>
          
          <div className="overflow-x-auto pb-4">
            <div className="flex gap-8 min-w-max">
              {topAreas.map((area, index) => (
                <div 
                  key={index} 
                  className="group cursor-pointer transition-all duration-300 hover:-translate-y-2 flex-shrink-0"
                >
                  <Card className="overflow-hidden rounded-2xl shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] transition-all duration-300 border-0 h-80 w-72">
                    <div className="relative h-full">
                      
                      {/* Background Image */}
                      <ImageWithFallback
                        src={area.image}
                        alt={area.area}
                        className="absolute inset-0 w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                      />
                      
                      {/* Gradient Overlay */}
                      <div className={`absolute inset-0 bg-gradient-to-t ${area.gradient}`}></div>
                      
                      {/* Content Overlay */}
                      <div className="relative h-full flex flex-col justify-between p-6 text-white">
                        
                        {/* Top Content */}
                        <div>
                          <h4 className="text-xl text-white group-hover:text-gold transition-colors">
                            {area.area}
                          </h4>
                        </div>
                        
                        {/* Bottom Content */}
                        <div className="space-y-3">
                          <div className="text-4xl text-gold group-hover:scale-105 transition-transform">
                            {area.growth}
                          </div>
                          <div className="text-lg text-white/90">
                            {area.avgPrice}
                          </div>
                        </div>
                        
                      </div>
                    </div>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Investment Highlights */}
        <div className="mt-20 text-center">
          <div className="bg-gradient-to-r from-soft-brown to-gold rounded-2xl p-12 text-white">
            <h3 className="mb-6 text-white">Why Invest in Dubai Real Estate?</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
              <div className="group">
                <div className="text-3xl mb-3 group-hover:scale-110 transition-transform">0%</div>
                <div className="text-white/90">Property Tax</div>
              </div>
              <div className="group">
                <div className="text-3xl mb-3 group-hover:scale-110 transition-transform">100%</div>
                <div className="text-white/90">Foreign Ownership</div>
              </div>
              <div className="group">
                <div className="text-3xl mb-3 group-hover:scale-110 transition-transform">8-12%</div>
                <div className="text-white/90">Rental Yields</div>
              </div>
              <div className="group">
                <div className="text-3xl mb-3 group-hover:scale-110 transition-transform">15%</div>
                <div className="text-white/90">Capital Growth</div>
              </div>
            </div>
            <p className="text-white/90 text-lg max-w-3xl mx-auto leading-relaxed">
              Dubai offers unparalleled opportunities for property investors with tax-free returns, 
              world-class infrastructure, and a strategic location connecting East and West.
            </p>
          </div>
        </div>

      </div>
    </section>
  );
}