import { useState } from 'react';
import { But<PERSON> } from './ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { 
  ArrowLeft, 
  Download, 
  FileText, 
  MapPin, 
  Calendar, 
  Building2, 
  Expand,
  Wifi,
  Car,
  Waves,
  Shield,
  Dumbbell,
  Baby,
  Trees,
  Users,
  Phone,
  Mail,
  Send,
  Maximize2,
  Eye,
  User,
  AtSign,
  CheckCircle2,
  Navigation
} from 'lucide-react';
import { ImageWithFallback } from './figma/ImageWithFallback';

interface Project {
  id: number;
  name: string;
  location: string;
  price: string;
  image: string;
  completion: string;
  description?: string;
  developer: string;
  status: string;
  coordinates?: [number, number];
}

interface PropertyDetailPageProps {
  project: Project;
  onBack: () => void;
}

export function PropertyDetailPage({ project, onBack }: PropertyDetailPageProps) {
  const [selectedTab, setSelectedTab] = useState('overview');
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedFloorPlan, setSelectedFloorPlan] = useState(0);
  const [isDownloadDialogOpen, setIsDownloadDialogOpen] = useState(false);
  const [downloadFormData, setDownloadFormData] = useState({
    name: '',
    email: ''
  });

  // Mock property coordinates - in real app, these would come from the project data
  const propertyCoordinates = project.coordinates || (() => {
    // Assign coordinates based on location or use default Dubai coordinates
    switch (project.location) {
      case 'Dubai Marina':
        return [25.0772, 55.1384];
      case 'Downtown Dubai':
        return [25.1972, 55.2744];
      case 'Palm Jumeirah':
        return [25.1124, 55.1390];
      case 'Business Bay':
        return [25.1870, 55.2631];
      case 'Dubai Creek Harbour':
        return [25.1838, 55.3167];
      case 'DIFC':
        return [25.2131, 55.2796];
      case 'JBR':
        return [25.0657, 55.1364];
      default:
        return [25.2048, 55.2708]; // Default Dubai coordinates
    }
  })();

  // Generate focused map URL for the property
  const generatePropertyMapUrl = () => {
    const [lat, lng] = propertyCoordinates;
    return `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3610.1234567890123!2d${lng}!3d${lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zM${lat.toFixed(6)}N%20${lng.toFixed(6)}E!5e0!3m2!1sen!2s!4v1703123456789!5m2!1sen!2s`;
  };

  const propertyImages = [
    "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
  ];

  const floorPlans = [
    {
      name: "1 Bedroom",
      size: "850 sq ft",
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      name: "2 Bedroom",
      size: "1,200 sq ft", 
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      name: "3 Bedroom",
      size: "1,650 sq ft",
      image: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    }
  ];

  const amenities = [
    { name: "High-Speed WiFi", icon: Wifi },
    { name: "Valet Parking", icon: Car },
    { name: "Swimming Pool", icon: Waves },
    { name: "24/7 Security", icon: Shield },
    { name: "Fitness Center", icon: Dumbbell },
    { name: "Kids Play Area", icon: Baby },
    { name: "Landscaped Gardens", icon: Trees },
    { name: "Community Lounge", icon: Users }
  ];

  // Enhanced PDF generation function with proper error handling
  const generatePropertyPDF = async () => {
    try {
      const { jsPDF } = await import('jspdf');
      
      const doc = new jsPDF();
      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;
      let currentY = 20;

      // Luxury brand colors (RGB values for jsPDF)
      const goldColor = [212, 175, 55];
      const softBrownColor = [139, 115, 85];
      const warmGrayColor = [138, 121, 104];
      const beigeColor = [245, 241, 235];

      // Safe text helper function
      const safeText = (text: any): string => {
        if (text === null || text === undefined) return '';
        return String(text);
      };

      // Helper function to add a new page if needed
      const checkAddPage = (requiredSpace: number): boolean => {
        if (currentY + requiredSpace > pageHeight - 20) {
          doc.addPage();
          currentY = 20;
          return true;
        }
        return false;
      };

      // Helper function to add luxury divider
      const addLuxuryDivider = (): void => {
        currentY += 10;
        const dividerY = currentY;
        
        // Gradient effect simulation with multiple lines
        for (let i = 0; i < 3; i++) {
          doc.setDrawColor(...goldColor);
          doc.setLineWidth(0.5);
          doc.line(20 + (i * 2), dividerY, pageWidth - 20 - (i * 2), dividerY);
        }
        currentY += 15;
      };

      // ============ COVER PAGE ============
      // Luxury header background
      doc.setFillColor(...softBrownColor);
      doc.rect(0, 0, pageWidth, 60, 'F');
      
      // Company logo area (placeholder)
      doc.setFillColor(...goldColor);
      doc.rect(20, 15, 30, 30, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('LOGO', 35, 35, { align: 'center' });
      
      // Company name and tagline
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(28);
      doc.setFont('helvetica', 'bold');
      doc.text('Smart Off Plan', 60, 28);
      
      doc.setFontSize(14);
      doc.setFont('helvetica', 'normal');
      doc.text('Premium Dubai Properties', 60, 38);
      
      doc.setFontSize(12);
      doc.text('INVEST SMART', 60, 48);

      currentY = 80;

      // Property title section
      doc.setFillColor(...beigeColor);
      doc.rect(20, currentY, pageWidth - 40, 40, 'F');
      
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(24);
      doc.setFont('helvetica', 'bold');
      doc.text(safeText(project.name), 30, currentY + 15);
      
      doc.setFontSize(14);
      doc.setFont('helvetica', 'normal');
      doc.text(`${safeText(project.location)} • ${safeText(project.status)}`, 30, currentY + 28);
      
      doc.setTextColor(...goldColor);
      doc.setFontSize(18);
      doc.setFont('helvetica', 'bold');
      doc.text(safeText(project.price), pageWidth - 30, currentY + 20, { align: 'right' });

      currentY += 60;

      // Property image placeholder
      doc.setFillColor(245, 245, 245);
      doc.rect(20, currentY, pageWidth - 40, 80, 'F');
      doc.setTextColor(...warmGrayColor);
      doc.setFontSize(12);
      doc.text('Property Image Gallery', pageWidth / 2, currentY + 40, { align: 'center' });
      doc.text('(4 Premium Images Available)', pageWidth / 2, currentY + 52, { align: 'center' });

      currentY += 100;

      // Property highlights
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Property Highlights', 20, currentY);
      currentY += 15;

      const highlights = [
        `Developer: ${safeText(project.developer)}`,
        `Completion: ${safeText(project.completion)}`,
        'Property Type: Luxury Apartment',
        'Available Units: 1-3 Bedrooms',
        `Starting Price: ${safeText(project.price)}`,
        'Payment Plan: Flexible Options Available'
      ];

      doc.setTextColor(...warmGrayColor);
      doc.setFontSize(11);
      doc.setFont('helvetica', 'normal');

      highlights.forEach((highlight) => {
        checkAddPage(8);
        doc.text(`• ${safeText(highlight)}`, 25, currentY);
        currentY += 8;
      });

      addLuxuryDivider();

      // ============ PROPERTY DESCRIPTION ============
      checkAddPage(40);
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(18);
      doc.setFont('helvetica', 'bold');
      doc.text('Property Description', 20, currentY);
      currentY += 15;

      const description = project.description || 
        "Experience luxury living at its finest with this exceptional property offering. Located in one of Dubai's most prestigious neighborhoods, this development combines modern architecture with world-class amenities. Each residence is thoughtfully designed with premium finishes, spacious layouts, and stunning views of the city skyline.";

      doc.setTextColor(...warmGrayColor);
      doc.setFontSize(11);
      doc.setFont('helvetica', 'normal');

      const descriptionLines = doc.splitTextToSize(safeText(description), pageWidth - 50);
      descriptionLines.forEach((line: string) => {
        checkAddPage(6);
        doc.text(safeText(line), 25, currentY);
        currentY += 6;
      });

      addLuxuryDivider();

      // ============ KEY FEATURES ============
      checkAddPage(30);
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Key Features', 20, currentY);
      currentY += 15;

      const features = [
        'Premium finishes throughout',
        'Floor-to-ceiling windows',
        'Modern kitchen appliances',
        'Smart home technology',
        'Private balcony/terrace',
        'Premium bathroom fixtures',
        'Built-in wardrobes',
        'Central air conditioning'
      ];

      // Two-column layout for features
      const midPoint = Math.ceil(features.length / 2);
      const leftFeatures = features.slice(0, midPoint);
      const rightFeatures = features.slice(midPoint);

      doc.setTextColor(...warmGrayColor);
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');

      leftFeatures.forEach((feature, index) => {
        doc.text(`✓ ${safeText(feature)}`, 25, currentY + (index * 8));
      });

      rightFeatures.forEach((feature, index) => {
        doc.text(`✓ ${safeText(feature)}`, 120, currentY + (index * 8));
      });

      currentY += (midPoint * 8) + 10;

      addLuxuryDivider();

      // ============ FLOOR PLANS ============
      checkAddPage(50);
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(18);
      doc.setFont('helvetica', 'bold');
      doc.text('Available Floor Plans', 20, currentY);
      currentY += 20;

      floorPlans.forEach((plan, index) => {
        checkAddPage(35);
        
        // Floor plan box
        doc.setFillColor(...beigeColor);
        doc.rect(20, currentY, pageWidth - 40, 30, 'F');
        
        doc.setTextColor(...softBrownColor);
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text(safeText(plan.name), 30, currentY + 12);
        
        doc.setTextColor(...warmGrayColor);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'normal');
        doc.text(`Total Area: ${safeText(plan.size)}`, 30, currentY + 22);
        
        // Floor plan image placeholder
        doc.setFillColor(235, 235, 235);
        doc.rect(pageWidth - 80, currentY + 5, 50, 20, 'F');
        doc.setTextColor(...warmGrayColor);
        doc.setFontSize(8);
        doc.text('Floor Plan', pageWidth - 55, currentY + 16, { align: 'center' });
        
        currentY += 40;
      });

      addLuxuryDivider();

      // ============ AMENITIES ============
      checkAddPage(40);
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(18);
      doc.setFont('helvetica', 'bold');
      doc.text('Premium Amenities', 20, currentY);
      currentY += 20;

      // Amenities in grid layout
      amenities.forEach((amenity, index) => {
        const col = index % 2;
        const row = Math.floor(index / 2);
        
        if (col === 0 && row > 0) {
          currentY += 10;
          checkAddPage(10);
        }

        const x = 25 + (col * (pageWidth - 50) / 2);
        
        doc.setTextColor(...warmGrayColor);
        doc.setFontSize(11);
        doc.setFont('helvetica', 'normal');
        doc.text(`• ${safeText(amenity.name)}`, x, currentY + (row === 0 ? 0 : 0));
      });

      currentY += Math.ceil(amenities.length / 2) * 10 + 10;

      addLuxuryDivider();

      // ============ LOCATION INFORMATION ============
      checkAddPage(40);
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(18);
      doc.setFont('helvetica', 'bold');
      doc.text('Prime Location', 20, currentY);
      currentY += 20;

      const locationInfo = [
        { landmark: 'Dubai Mall', distance: '5 minutes drive' },
        { landmark: 'Dubai Metro', distance: '3 minutes walk' },
        { landmark: 'Dubai International Airport', distance: '20 minutes drive' },
        { landmark: 'Business Bay', distance: '8 minutes drive' },
        { landmark: 'Burj Khalifa', distance: '10 minutes drive' },
        { landmark: 'Dubai Marina Walk', distance: '2 minutes walk' }
      ];

      doc.setTextColor(...warmGrayColor);
      doc.setFontSize(11);
      doc.setFont('helvetica', 'normal');

      locationInfo.forEach((info) => {
        checkAddPage(8);
        doc.text(`📍 ${safeText(info.landmark)}`, 25, currentY);
        doc.text(safeText(info.distance), pageWidth - 25, currentY, { align: 'right' });
        currentY += 8;
      });

      addLuxuryDivider();

      // ============ INVESTMENT HIGHLIGHTS ============
      checkAddPage(40);
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(18);
      doc.setFont('helvetica', 'bold');
      doc.text('Investment Highlights', 20, currentY);
      currentY += 20;

      const investmentHighlights = [
        { title: '0% Property Tax', description: 'No annual property taxes on real estate investments' },
        { title: '100% Foreign Ownership', description: 'Full ownership rights for international investors' },
        { title: '8-12% Rental Yields', description: 'Attractive annual rental returns in Dubai' },
        { title: '15% Capital Growth', description: 'Strong historical appreciation potential' }
      ];

      investmentHighlights.forEach((highlight) => {
        checkAddPage(15);
        
        doc.setTextColor(...goldColor);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text(`✓ ${safeText(highlight.title)}`, 25, currentY);
        
        doc.setTextColor(...warmGrayColor);
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');
        const lines = doc.splitTextToSize(safeText(highlight.description), pageWidth - 60);
        lines.forEach((line: string, lineIndex: number) => {
          doc.text(safeText(line), 35, currentY + 8 + (lineIndex * 5));
        });
        
        currentY += 20;
      });

      // Add new page for contact section
      doc.addPage();
      currentY = 20;

      // ============ CONTACT US SECTION ============
      // Contact header with luxury styling
      doc.setFillColor(...goldColor);
      doc.rect(0, 0, pageWidth, 50, 'F');
      
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(24);
      doc.setFont('helvetica', 'bold');
      doc.text('Contact Our Expert Team', 20, 20);
      
      doc.setFontSize(14);
      doc.setFont('helvetica', 'normal');
      doc.text('Ready to invest in Dubai\'s premium properties? Get in touch today.', 20, 35);

      currentY = 70;

      // Contact information in structured layout
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(18);
      doc.setFont('helvetica', 'bold');
      doc.text('Get In Touch', 20, currentY);
      currentY += 20;

      // Contact details with icons
      const contactInfo = [
        { icon: '📞', label: 'Phone', value: '+971 4 123 4567', description: 'Available 24/7 for international clients' },
        { icon: '📧', label: 'Email', value: '<EMAIL>', description: 'Get detailed property information' },
        { icon: '📍', label: 'Office', value: 'Business Bay, Dubai, UAE', description: 'Visit our luxury showroom' },
        { icon: '🕒', label: 'Hours', value: 'Mon-Fri: 9AM-7PM, Sat: 10AM-4PM', description: 'Extended hours for international clients' }
      ];

      contactInfo.forEach((contact) => {
        checkAddPage(20);
        
        // Contact item background
        doc.setFillColor(...beigeColor);
        doc.rect(20, currentY, pageWidth - 40, 18, 'F');
        
        doc.setTextColor(...goldColor);
        doc.setFontSize(16);
        doc.text(safeText(contact.icon), 25, currentY + 8);
        
        doc.setTextColor(...softBrownColor);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text(safeText(contact.label), 35, currentY + 8);
        
        doc.setTextColor(...warmGrayColor);
        doc.setFont('helvetica', 'normal');
        doc.text(safeText(contact.value), 35, currentY + 15);
        
        doc.setFontSize(9);
        doc.text(safeText(contact.description), pageWidth - 25, currentY + 12, { align: 'right' });
        
        currentY += 25;
      });

      addLuxuryDivider();

      // ============ WHY CHOOSE SMART OFF PLAN ============
      checkAddPage(30);
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Why Choose Smart Off Plan?', 20, currentY);
      currentY += 15;

      const whyChooseUs = [
        'Trusted by international investors since 2020',
        'Exclusive access to premium off-plan properties',
        'Comprehensive after-sales support',
        'Expert guidance through the entire process',
        'Transparent pricing with no hidden fees',
        'Legal assistance and documentation support'
      ];

      doc.setTextColor(...warmGrayColor);
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');

      whyChooseUs.forEach((reason) => {
        checkAddPage(8);
        doc.text(`✓ ${safeText(reason)}`, 25, currentY);
        currentY += 8;
      });

      addLuxuryDivider();

      // ============ NEXT STEPS ============
      checkAddPage(40);
      doc.setTextColor(...softBrownColor);
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Next Steps', 20, currentY);
      currentY += 15;

      const nextSteps = [
        { step: '1', title: 'Schedule Consultation', description: 'Book a free consultation with our property experts' },
        { step: '2', title: 'Property Viewing', description: 'Visit the property or take a virtual tour' },
        { step: '3', title: 'Secure Your Unit', description: 'Reserve your preferred unit with flexible payment plans' },
        { step: '4', title: 'Complete Purchase', description: 'Finalize documentation with our legal support team' }
      ];

      nextSteps.forEach((step) => {
        checkAddPage(15);
        
        doc.setFillColor(...goldColor);
        doc.circle(25, currentY + 5, 8, 'F');
        
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text(safeText(step.step), 25, currentY + 8, { align: 'center' });
        
        doc.setTextColor(...softBrownColor);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text(safeText(step.title), 40, currentY + 5);
        
        doc.setTextColor(...warmGrayColor);
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');
        doc.text(safeText(step.description), 40, currentY + 12);
        
        currentY += 20;
      });

      // ============ FOOTER ============
      // Professional footer
      const footerY = pageHeight - 40;
      doc.setFillColor(...softBrownColor);
      doc.rect(0, footerY, pageWidth, 40, 'F');
      
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.text('Smart Off Plan - Premium Dubai Properties', 20, footerY + 15);
      
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text('Business Bay, Dubai, UAE | +971 4 123 4567 | <EMAIL>', 20, footerY + 25);
      
      doc.setTextColor(...goldColor);
      doc.text('www.smartoffplan.ae', 20, footerY + 32);
      
      doc.setTextColor(255, 255, 255);
      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, pageWidth - 20, footerY + 25, { align: 'right' });
      doc.text(`For: ${safeText(downloadFormData.name) || 'Valued Client'}`, pageWidth - 20, footerY + 32, { align: 'right' });

      // Save the PDF with property name
      const fileName = `${safeText(project.name).replace(/[^a-z0-9]/gi, '_')}_Property_Brochure.pdf`;
      doc.save(fileName);

    } catch (error) {
      console.error('Error generating PDF:', error);
      // Fallback to simple download
      const fallbackContent = `
${project.name || 'Property'} - Property Brochure
=================================

Location: ${project.location || 'Dubai'}
Price: ${project.price || 'Contact for pricing'}
Developer: ${project.developer || 'Premium Developer'}
Status: ${project.status || 'Available'}
Completion: ${project.completion || 'To be announced'}

Contact Smart Off Plan:
Phone: +971 4 123 4567
Email: <EMAIL>
Office: Business Bay, Dubai, UAE

For the complete property brochure with images and floor plans, 
please contact us directly.
      `;
      
      const link = document.createElement('a');
      link.href = 'data:text/plain;charset=utf-8,' + encodeURIComponent(fallbackContent);
      link.download = `${(project.name || 'Property').replace(/[^a-z0-9]/gi, '_')}_Property_Info.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleDownloadSubmit = () => {
    generatePropertyPDF();
    setIsDownloadDialogOpen(false);
  };

  const handleFormChange = (field: string, value: string) => {
    setDownloadFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="bg-ivory min-h-screen">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={onBack}
          className="mb-8 text-soft-brown hover:text-gold hover:bg-beige/50"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Properties
        </Button>

        {/* Property Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
          <div>
            <div className="mb-3">
              <p className="text-gold text-sm font-medium uppercase tracking-wider">{project.name}</p>
              <h1 className="text-soft-brown mb-2">{project.name}</h1>
            </div>
            <div className="flex items-center text-warm-gray">
              <span>{project.name}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button variant="outline" className="border-gold text-gold hover:bg-gold hover:text-white">
              <Eye className="w-4 h-4 mr-2" />
              360° Tour
            </Button>
            
            <Dialog open={isDownloadDialogOpen} onOpenChange={setIsDownloadDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-soft-brown hover:bg-soft-brown/90 text-white">
                  <Download className="w-4 h-4 mr-2" />
                  Download Brochure
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-soft-brown">Download Property Brochure</DialogTitle>
                  <DialogDescription>
                    Get instant access to detailed property information including floor plans, amenities, pricing details, and our complete contact information.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <p className="text-warm-gray text-sm">
                    Enter your details to receive the complete property brochure with contact information.
                  </p>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="download-name">Full Name</Label>
                      <Input
                        id="download-name"
                        placeholder="Enter your name"
                        value={downloadFormData.name}
                        onChange={(e) => handleFormChange('name', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="download-email">Email</Label>
                      <Input
                        id="download-email"
                        type="email"
                        placeholder="Enter your email"
                        value={downloadFormData.email}
                        onChange={(e) => handleFormChange('email', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                  </div>
                  <Button 
                    onClick={handleDownloadSubmit}
                    className="w-full bg-soft-brown hover:bg-soft-brown/90 text-white"
                    disabled={!downloadFormData.name || !downloadFormData.email}
                  >
                    <CheckCircle2 className="w-4 h-4 mr-2" />
                    Download Complete Brochure
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          
          {/* Left Column - Images and Details */}
          <div className="lg:col-span-2 space-y-8">
            
            {/* Property Images */}
            <div className="space-y-4">
              {/* Main Image */}
              <div className="relative aspect-[16/10] rounded-2xl overflow-hidden">
                <ImageWithFallback
                  src={propertyImages[selectedImageIndex]}
                  alt={project.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-4 left-4">
                  <Badge className="bg-gold text-white">Featured</Badge>
                </div>
                <button className="absolute top-4 right-4 w-10 h-10 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors">
                  <Maximize2 className="w-5 h-5 text-soft-brown" />
                </button>
              </div>
              
              {/* Thumbnail Images */}
              <div className="grid grid-cols-4 gap-4">
                {propertyImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`relative aspect-[4/3] rounded-lg overflow-hidden ${
                      selectedImageIndex === index ? 'ring-2 ring-gold' : ''
                    }`}
                  >
                    <ImageWithFallback
                      src={image}
                      alt={`${project.name} view ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>

            {/* Property Details Tabs */}
            <div className="space-y-6">
              {/* Tab Navigation */}
              <div className="flex border-b border-border">
                {[
                  { id: 'overview', label: 'Overview' },
                  { id: 'floor-plans', label: 'Floor Plans' },
                  { id: 'amenities', label: 'Amenities' },
                  { id: 'location', label: 'Location' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setSelectedTab(tab.id)}
                    className={`px-6 py-3 text-sm transition-colors ${
                      selectedTab === tab.id
                        ? 'text-soft-brown border-b-2 border-gold'
                        : 'text-warm-gray hover:text-soft-brown'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>

              {/* Tab Content */}
              <div className="space-y-6">
                {selectedTab === 'overview' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-soft-brown mb-4">Property Description</h3>
                      <p className="text-warm-gray leading-relaxed">
                        {project.description || "Experience luxury living at its finest with this exceptional property offering. Located in one of Dubai's most prestigious neighborhoods, this development combines modern architecture with world-class amenities. Each residence is thoughtfully designed with premium finishes, spacious layouts, and stunning views of the city skyline."}
                      </p>
                    </div>
                    
                    <div>
                      <h4 className="text-soft-brown mb-3">Key Features</h4>
                      <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <li className="flex items-center text-warm-gray">
                          <span className="w-2 h-2 bg-gold rounded-full mr-3"></span>
                          Premium finishes throughout
                        </li>
                        <li className="flex items-center text-warm-gray">
                          <span className="w-2 h-2 bg-gold rounded-full mr-3"></span>
                          Floor-to-ceiling windows
                        </li>
                        <li className="flex items-center text-warm-gray">
                          <span className="w-2 h-2 bg-gold rounded-full mr-3"></span>
                          Modern kitchen appliances
                        </li>
                        <li className="flex items-center text-warm-gray">
                          <span className="w-2 h-2 bg-gold rounded-full mr-3"></span>
                          Smart home technology
                        </li>
                        <li className="flex items-center text-warm-gray">
                          <span className="w-2 h-2 bg-gold rounded-full mr-3"></span>
                          Private balcony/terrace
                        </li>
                        <li className="flex items-center text-warm-gray">
                          <span className="w-2 h-2 bg-gold rounded-full mr-3"></span>
                          Premium bathroom fixtures
                        </li>
                      </ul>
                    </div>
                  </div>
                )}

                {selectedTab === 'floor-plans' && (
                  <div className="space-y-6">
                    <div className="flex space-x-4 mb-6">
                      {floorPlans.map((plan, index) => (
                        <button
                          key={index}
                          onClick={() => setSelectedFloorPlan(index)}
                          className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                            selectedFloorPlan === index
                              ? 'bg-gold text-white'
                              : 'bg-beige text-soft-brown hover:bg-gold hover:text-white'
                          }`}
                        >
                          {plan.name}
                        </button>
                      ))}
                    </div>
                    
                    <div className="bg-white rounded-2xl p-6 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-soft-brown">{floorPlans[selectedFloorPlan].name} Floor Plan</h4>
                        <div className="text-right">
                          <p className="text-warm-gray text-sm">Total Area</p>
                          <p className="text-soft-brown">{floorPlans[selectedFloorPlan].size}</p>
                        </div>
                      </div>
                      
                      <div className="aspect-[4/3] rounded-lg overflow-hidden bg-beige">
                        <ImageWithFallback
                          src={floorPlans[selectedFloorPlan].image}
                          alt={`${floorPlans[selectedFloorPlan].name} floor plan`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {selectedTab === 'amenities' && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                    {amenities.map((amenity, index) => {
                      const IconComponent = amenity.icon;
                      return (
                        <div key={index} className="text-center">
                          <div className="w-16 h-16 bg-beige rounded-full flex items-center justify-center mx-auto mb-3">
                            <IconComponent className="w-8 h-8 text-soft-brown" />
                          </div>
                          <p className="text-sm text-warm-gray">{amenity.name}</p>
                        </div>
                      );
                    })}
                  </div>
                )}

                {selectedTab === 'location' && (
                  <div className="space-y-6">
                    <div className="aspect-[16/9] rounded-2xl overflow-hidden bg-beige">
                      <div className="w-full h-full flex items-center justify-center">
                        <div className="text-center">
                          <MapPin className="w-12 h-12 text-soft-brown mx-auto mb-4" />
                          <p className="text-warm-gray">Interactive map coming soon</p>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-soft-brown mb-4">Nearby Landmarks</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-2 h-2 bg-gold rounded-full"></div>
                          <div>
                            <p className="text-soft-brown text-sm">Dubai Mall</p>
                            <p className="text-warm-gray text-xs">5 minutes drive</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="w-2 h-2 bg-gold rounded-full"></div>
                          <div>
                            <p className="text-soft-brown text-sm">Dubai Metro</p>
                            <p className="text-warm-gray text-xs">3 minutes walk</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="w-2 h-2 bg-gold rounded-full"></div>
                          <div>
                            <p className="text-soft-brown text-sm">Dubai International Airport</p>
                            <p className="text-warm-gray text-xs">20 minutes drive</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="w-2 h-2 bg-gold rounded-full"></div>
                          <div>
                            <p className="text-soft-brown text-sm">Business Bay</p>
                            <p className="text-warm-gray text-xs">8 minutes drive</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Property Info */}
          <div className="space-y-6">
            
            {/* Property Location Map */}
            <Card className="overflow-hidden h-[280px] shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] border-0 rounded-2xl">
              <CardHeader className="pb-3">
                <CardTitle className="text-soft-brown flex items-center justify-between text-lg">
                  <div className="flex items-center space-x-2">
                    <Navigation className="w-5 h-5" />
                    <span>Property Location</span>
                  </div>
                  <Badge variant="outline" className="text-gold border-gold text-sm">
                    {project.location}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="relative h-[200px] overflow-hidden">
                  {/* Google Maps Embed for Specific Property */}
                  <iframe
                    src={generatePropertyMapUrl()}
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    className="absolute inset-0"
                  />
                  
                  {/* Property Marker Overlay */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none">
                    <div className="w-10 h-10 bg-gold border-3 border-white rounded-full shadow-lg flex items-center justify-center pulse-animation">
                      <MapPin className="w-5 h-5 text-white" />
                    </div>
                  </div>
                  

                  
                  {/* Map Controls */}
                  <div className="absolute top-3 right-3">
                    <button className="w-8 h-8 bg-white/95 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg hover:bg-white hover:shadow-xl transition-all duration-300 border border-gold/20">
                      <Maximize2 className="w-4 h-4 text-soft-brown" />
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Property Header */}
            <Card className="shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] border-0 rounded-2xl">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <div className="mb-2">
                      <h1 className="text-soft-brown mb-[12px] text-[32px] mt-[0px] mr-[0px] ml-[0px]">{project.name}</h1>
                      {project.title && project.title !== project.name && (
                        <p className="text-gold text-lg font-medium -mt-2 mb-2">{project.title}</p>
                      )}
                    </div>
                    <div className="flex items-center text-warm-gray mb-4">
                      <MapPin className="w-4 h-4 mr-1" />
                      <span className="text-sm">{project.location}</span>
                    </div>
                  </div>
                  <Badge variant="outline" className="text-gold border-gold">
                    {project.status}
                  </Badge>
                </div>
                
                <div className="flex items-center space-x-6">
                  <div>
                    <p className="text-warm-gray text-sm">Starting from</p>
                    <p className="text-soft-brown">{project.price}</p>
                  </div>
                  <div>
                    <p className="text-warm-gray text-sm">Completion</p>
                    <p className="text-soft-brown">{project.completion}</p>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Quick Stats */}
            <Card className="shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] border-0 rounded-2xl">
              <CardHeader>
                <CardTitle className="text-soft-brown">Property Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-warm-gray">Property Type</span>
                    <span className="text-soft-brown">Apartment</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-warm-gray">Developer</span>
                    <span className="text-soft-brown">{project.developer}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-warm-gray">Units Available</span>
                    <span className="text-soft-brown">1-3 Bedrooms</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-warm-gray">Payment Plan</span>
                    <span className="text-soft-brown">Flexible</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Form */}
            <Card className="shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] border-0 rounded-2xl">
              <CardHeader>
                <CardTitle className="text-soft-brown">Get More Information</CardTitle>
                <p className="text-warm-gray text-sm">Our experts will contact you within 24 hours</p>
              </CardHeader>
              <CardContent>
                <form className="space-y-4">
                  <div>
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      type="text"
                      placeholder="Enter your name"
                      className="mt-1 rounded-xl border-border/30 focus:border-gold focus:ring-gold"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      className="mt-1 rounded-xl border-border/30 focus:border-gold focus:ring-gold"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="Enter your phone"
                      className="mt-1 rounded-xl border-border/30 focus:border-gold focus:ring-gold"
                    />
                  </div>
                  <div>
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      placeholder="Tell us about your requirements..."
                      rows={3}
                      className="mt-1 rounded-xl border-border/30 focus:border-gold focus:ring-gold resize-none"
                    />
                  </div>
                  <Button className="w-full bg-soft-brown hover:bg-soft-brown/90 text-white rounded-xl">
                    <Send className="w-4 h-4 mr-2" />
                    Send Inquiry
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Contact Info */}
            <Card className="shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] border-0 rounded-2xl">
              <CardContent className="pt-6">
                <div className="text-center">
                  <h4 className="text-soft-brown mb-4">Need Help?</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-center">
                      <Phone className="w-4 h-4 mr-2 text-gold" />
                      <span className="text-warm-gray">+971 4 123 4567</span>
                    </div>
                    <div className="flex items-center justify-center">
                      <Mail className="w-4 h-4 mr-2 text-gold" />
                      <span className="text-warm-gray"><EMAIL></span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

          </div>
        </div>
      </div>
    </div>
  );
}