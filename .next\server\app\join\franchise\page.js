/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/join/franchise/page";
exports.ids = ["app/join/franchise/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3fb1e4adba92\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmxhY2tcXERlc2t0b3BcXHNtYXJ0LW9mZi1wbGFuXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiM2ZiMWU0YWRiYTkyXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/join/franchise/page.tsx":
/*!*************************************!*\
  !*** ./app/join/franchise/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\smart-off-plan\\app\\join\\franchise\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'Smart Off Plan - Dubai Property Investment',\n    description: 'Your trusted partner for Dubai property investments. Connecting international investors with premium off-plan opportunities.',\n    keywords: 'Dubai property, off-plan investment, real estate, property management, golden visa',\n    authors: [\n        {\n            name: 'Smart Off Plan'\n        }\n    ],\n    openGraph: {\n        title: 'Smart Off Plan - Dubai Property Investment',\n        description: 'Your trusted partner for Dubai property investments. Connecting international investors with premium off-plan opportunities.',\n        type: 'website',\n        locale: 'en_US'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7SUFDVkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBaUI7S0FBRTtJQUNyQ0MsV0FBVztRQUNUTCxPQUFPO1FBQ1BDLGFBQWE7UUFDYkssTUFBTTtRQUNOQyxRQUFRO0lBQ1Y7QUFDRixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVU7c0JBQ2JKOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEJsYWNrXFxEZXNrdG9wXFxzbWFydC1vZmYtcGxhblxcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1NtYXJ0IE9mZiBQbGFuIC0gRHViYWkgUHJvcGVydHkgSW52ZXN0bWVudCcsXG4gIGRlc2NyaXB0aW9uOiAnWW91ciB0cnVzdGVkIHBhcnRuZXIgZm9yIER1YmFpIHByb3BlcnR5IGludmVzdG1lbnRzLiBDb25uZWN0aW5nIGludGVybmF0aW9uYWwgaW52ZXN0b3JzIHdpdGggcHJlbWl1bSBvZmYtcGxhbiBvcHBvcnR1bml0aWVzLicsXG4gIGtleXdvcmRzOiAnRHViYWkgcHJvcGVydHksIG9mZi1wbGFuIGludmVzdG1lbnQsIHJlYWwgZXN0YXRlLCBwcm9wZXJ0eSBtYW5hZ2VtZW50LCBnb2xkZW4gdmlzYScsXG4gIGF1dGhvcnM6IFt7IG5hbWU6ICdTbWFydCBPZmYgUGxhbicgfV0sXG4gIG9wZW5HcmFwaDoge1xuICAgIHRpdGxlOiAnU21hcnQgT2ZmIFBsYW4gLSBEdWJhaSBQcm9wZXJ0eSBJbnZlc3RtZW50JyxcbiAgICBkZXNjcmlwdGlvbjogJ1lvdXIgdHJ1c3RlZCBwYXJ0bmVyIGZvciBEdWJhaSBwcm9wZXJ0eSBpbnZlc3RtZW50cy4gQ29ubmVjdGluZyBpbnRlcm5hdGlvbmFsIGludmVzdG9ycyB3aXRoIHByZW1pdW0gb2ZmLXBsYW4gb3Bwb3J0dW5pdGllcy4nLFxuICAgIHR5cGU6ICd3ZWJzaXRlJyxcbiAgICBsb2NhbGU6ICdlbl9VUycsXG4gIH0sXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYW50aWFsaWFzZWRcIj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiYXV0aG9ycyIsIm5hbWUiLCJvcGVuR3JhcGgiLCJ0eXBlIiwibG9jYWxlIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fjoin%2Ffranchise%2Fpage&page=%2Fjoin%2Ffranchise%2Fpage&appPaths=%2Fjoin%2Ffranchise%2Fpage&pagePath=private-next-app-dir%2Fjoin%2Ffranchise%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fjoin%2Ffranchise%2Fpage&page=%2Fjoin%2Ffranchise%2Fpage&appPaths=%2Fjoin%2Ffranchise%2Fpage&pagePath=private-next-app-dir%2Fjoin%2Ffranchise%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/join/franchise/page.tsx */ \"(rsc)/./app/join/franchise/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'join',\n        {\n        children: [\n        'franchise',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/join/franchise/page\",\n        pathname: \"/join/franchise\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fjoin%2Ffranchise%2Fpage&page=%2Fjoin%2Ffranchise%2Fpage&appPaths=%2Fjoin%2Ffranchise%2Fpage&pagePath=private-next-app-dir%2Fjoin%2Ffranchise%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cjoin%5C%5Cfranchise%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cjoin%5C%5Cfranchise%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/join/franchise/page.tsx */ \"(rsc)/./app/join/franchise/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0JsYWNrJTVDJTVDRGVza3RvcCU1QyU1Q3NtYXJ0LW9mZi1wbGFuJTVDJTVDYXBwJTVDJTVDam9pbiU1QyU1Q2ZyYW5jaGlzZSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBOEciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEJsYWNrXFxcXERlc2t0b3BcXFxcc21hcnQtb2ZmLXBsYW5cXFxcYXBwXFxcXGpvaW5cXFxcZnJhbmNoaXNlXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cjoin%5C%5Cfranchise%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/BecomeFranchiseePage.tsx":
/*!*************************************************!*\
  !*** ./app/components/BecomeFranchiseePage.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BecomeFranchiseePage: () => (/* binding */ BecomeFranchiseePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./app/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./app/components/ui/card.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/input */ \"(ssr)/./app/components/ui/input.tsx\");\n/* harmony import */ var _ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/textarea */ \"(ssr)/./app/components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building2,Calendar,CheckCircle,Crown,DollarSign,Globe,Phone,Shield,Store,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n\n\n\n\n\n\n\nfunction BecomeFranchiseePage({ onBack }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        phone: '',\n        location: '',\n        investment: '',\n        experience: '',\n        timeline: '',\n        message: ''\n    });\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        console.log('Franchise application submitted:', formData);\n        // Handle form submission here\n        setFormData({\n            name: '',\n            email: '',\n            phone: '',\n            location: '',\n            investment: '',\n            experience: '',\n            timeline: '',\n            message: ''\n        });\n    };\n    const franchiseBenefits = [\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Exclusive Territory Rights\",\n            description: \"Protected territory with exclusive rights to operate under Smart Off Plan brand\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Proven Business Model\",\n            description: \"Access to our successful, tested business framework and operational systems\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: \"Brand Recognition\",\n            description: \"Leverage the Smart Off Plan brand reputation and market presence\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: \"Comprehensive Training\",\n            description: \"6-week intensive training program covering all aspects of the business\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"Marketing Support\",\n            description: \"National advertising campaigns and local marketing assistance\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"Ongoing Support\",\n            description: \"Continuous operational, technical, and business development support\"\n        }\n    ];\n    const investmentDetails = [\n        {\n            title: \"Initial Franchise Fee\",\n            amount: \"AED 150,000\",\n            description: \"One-time franchise licensing fee\"\n        },\n        {\n            title: \"Setup Investment\",\n            amount: \"AED 300,000 - 500,000\",\n            description: \"Office setup, equipment, and initial marketing\"\n        },\n        {\n            title: \"Working Capital\",\n            amount: \"AED 200,000\",\n            description: \"Recommended operating capital for first 6 months\"\n        },\n        {\n            title: \"Ongoing Royalty\",\n            amount: \"8% of Revenue\",\n            description: \"Monthly royalty fee on gross revenue\"\n        }\n    ];\n    const requirements = [\n        \"Minimum liquid capital of AED 750,000\",\n        \"Business management experience (5+ years)\",\n        \"Commitment to full-time operation\",\n        \"Strong local market knowledge\",\n        \"Excellent communication skills\",\n        \"Passion for real estate and customer service\"\n    ];\n    const supportServices = [\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Business Operations\",\n            description: \"Comprehensive operations manual, policies, and procedures\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: \"Staff Training\",\n            description: \"Initial and ongoing training programs for your team\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"Lead Generation\",\n            description: \"Access to national lead generation and referral systems\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Performance Analytics\",\n            description: \"Advanced reporting tools and business intelligence systems\"\n        }\n    ];\n    const processSteps = [\n        {\n            step: \"1\",\n            title: \"Initial Application\",\n            description: \"Submit your franchise application and investment details\"\n        },\n        {\n            step: \"2\",\n            title: \"Review & Interview\",\n            description: \"Application review and personal interview with our team\"\n        },\n        {\n            step: \"3\",\n            title: \"Discovery Process\",\n            description: \"Visit existing franchises and review detailed business information\"\n        },\n        {\n            step: \"4\",\n            title: \"Final Approval\",\n            description: \"Final approval and franchise agreement signing\"\n        },\n        {\n            step: \"5\",\n            title: \"Training & Launch\",\n            description: \"Complete training program and grand opening support\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-ivory pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-soft-brown text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onBack,\n                                className: \"text-white hover:bg-white/10 mr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Back to Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gold rounded-2xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-8 h-8 text-soft-brown\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-white mb-6 text-[48px]\",\n                                    children: \"Become a Franchisee\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-tan text-xl leading-relaxed\",\n                                    children: \"Own your own Smart Off Plan franchise and capitalize on Dubai's booming real estate market with our proven business model and brand support.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-soft-brown mb-6 text-[36px]\",\n                                    children: \"Why Choose Our Franchise?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-warm-gray text-lg max-w-3xl mx-auto\",\n                                    children: \"Join a proven business model with comprehensive support, exclusive territory rights, and the backing of Dubai's leading off-plan property specialists.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: franchiseBenefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-white rounded-3xl shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_12px_40px_-4px_rgba(139,115,85,0.15),0_6px_20px_-4px_rgba(139,115,85,0.1)] hover:-translate-y-2 transition-all duration-300 border-0 overflow-hidden group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-8 text-center h-full flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-beige to-ivory rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(benefit.icon, {\n                                                    className: \"w-8 h-8 text-gold\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-soft-brown mb-4 group-hover:text-gold transition-colors\",\n                                                children: benefit.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-warm-gray text-sm leading-relaxed flex-grow\",\n                                                children: benefit.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-gradient-to-br from-beige to-ivory\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-soft-brown mb-8\",\n                                        children: \"Investment Requirements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: investmentDetails.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-2xl p-6 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-soft-brown\",\n                                                                children: detail.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gold font-semibold text-lg\",\n                                                                children: detail.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-warm-gray text-sm\",\n                                                        children: detail.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 bg-gradient-to-r from-gold/10 via-gold/5 to-gold/10 rounded-2xl p-6 border border-gold/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-soft-brown mb-3\",\n                                                children: \"ROI Potential\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-warm-gray text-sm mb-4\",\n                                                children: \"Our existing franchisees typically see break-even within 12-18 months with potential annual revenues of AED 2-5 million.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-soft-brown hover:bg-soft-brown/90 text-white px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Request Financial Projections\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-soft-brown mb-8\",\n                                        children: \"How It Works\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: processSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gold rounded-full flex items-center justify-center text-soft-brown font-semibold mr-4 flex-shrink-0\",\n                                                        children: step.step\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-soft-brown mb-2\",\n                                                                children: step.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-warm-gray text-sm leading-relaxed\",\n                                                                children: step.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-soft-brown mb-8\",\n                                        children: \"Franchisee Requirements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 mb-8\",\n                                        children: requirements.map((requirement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-5 h-5 text-gold mt-1 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-warm-gray\",\n                                                        children: requirement\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl p-6 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-soft-brown mb-4\",\n                                                children: \"Territory Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-warm-gray text-sm mb-4\",\n                                                children: \"We're currently seeking qualified franchisees for key markets across the UAE and select international locations.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-gold hover:bg-gold/90 text-soft-brown\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Check Territory Availability\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-soft-brown mb-8\",\n                                        children: \"Ongoing Support\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: supportServices.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-2xl p-6 shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)] hover:shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] hover:-translate-y-1 transition-all duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-beige rounded-xl flex items-center justify-center mr-4 flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(service.icon, {\n                                                                className: \"w-6 h-6 text-gold\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"text-soft-brown mb-2\",\n                                                                    children: service.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-warm-gray text-sm leading-relaxed\",\n                                                                    children: service.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-gradient-to-br from-beige to-ivory\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-soft-brown mb-6 text-[36px] text-[40px]\",\n                                        children: \"Franchise Application\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-warm-gray text-lg max-w-2xl mx-auto\",\n                                        children: \"Take the first step towards owning your Smart Off Plan franchise. Complete the confidential application below.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-3xl p-8 shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"name\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Full Name *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"name\",\n                                                            name: \"name\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            value: formData.name,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"Your full name\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"email\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Email Address *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"email\",\n                                                            name: \"email\",\n                                                            type: \"email\",\n                                                            required: true,\n                                                            value: formData.email,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"<EMAIL>\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"phone\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Phone Number *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"phone\",\n                                                            name: \"phone\",\n                                                            type: \"tel\",\n                                                            required: true,\n                                                            value: formData.phone,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"+971 50 123 4567\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"location\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Preferred Territory *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"location\",\n                                                            name: \"location\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            value: formData.location,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"e.g., Abu Dhabi, Sharjah\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"investment\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Available Investment Capital *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"investment\",\n                                                            name: \"investment\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            value: formData.investment,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"e.g., AED 1,000,000\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"timeline\",\n                                                            className: \"block text-sm text-soft-brown mb-2\",\n                                                            children: \"Desired Timeline *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"timeline\",\n                                                            name: \"timeline\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            value: formData.timeline,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"e.g., 3-6 months\",\n                                                            className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"experience\",\n                                                    className: \"block text-sm text-soft-brown mb-2\",\n                                                    children: \"Business/Real Estate Experience *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"experience\",\n                                                    name: \"experience\",\n                                                    type: \"text\",\n                                                    required: true,\n                                                    value: formData.experience,\n                                                    onChange: handleInputChange,\n                                                    placeholder: \"Describe your relevant experience\",\n                                                    className: \"w-full py-3 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"message\",\n                                                    className: \"block text-sm text-soft-brown mb-2\",\n                                                    children: \"Why do you want to become a Smart Off Plan franchisee? *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                    id: \"message\",\n                                                    name: \"message\",\n                                                    required: true,\n                                                    value: formData.message,\n                                                    onChange: handleInputChange,\n                                                    placeholder: \"Tell us about your motivation, goals, and what you can bring to our franchise network...\",\n                                                    rows: 6,\n                                                    className: \"w-full p-4 border border-soft-gray/30 rounded-xl focus:border-gold focus:ring-gold resize-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                className: \"bg-gold hover:bg-gold/90 text-soft-brown px-8 py-3 rounded-xl transition-all duration-300 hover:shadow-[0_4px_16px_-2px_rgba(212,175,55,0.3)] hover:scale-105\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Submit Application\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-soft-brown text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-white mb-6 text-[36px] text-[40px]\",\n                                children: \"Ready to Own Your Future?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-tan text-lg mb-8 leading-relaxed\",\n                                children: \"Join the Smart Off Plan franchise family and build a successful business in Dubai's thriving real estate market with our proven support system.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-gold hover:bg-gold/90 text-soft-brown px-8 py-3 text-lg text-[14px] text-[15px] text-[16px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Call Franchise Team\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        className: \"border-white text-[rgba(139,115,85,1)] hover:bg-white hover:text-soft-brown px-8 py-3 text-lg text-[14px] text-[15px] text-[16px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building2_Calendar_CheckCircle_Crown_DollarSign_Globe_Phone_Shield_Store_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Schedule Discovery Call\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                    lineNumber: 497,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\BecomeFranchiseePage.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/BecomeFranchiseePage.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./app/components/Navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./app/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _SmartOffPlanLogo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SmartOffPlanLogo */ \"(ssr)/./app/components/SmartOffPlanLogo.tsx\");\n\n\n\n\n\n\n\nfunction Navbar({ onNavigate, onLogoClick, currentPage }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Determine current page from pathname if not provided\n    const getCurrentPage = ()=>{\n        if (currentPage) return currentPage;\n        if (pathname === \"/\") return \"home\";\n        if (pathname === \"/properties\") return \"properties\";\n        if (pathname === \"/developers\") return \"developers\";\n        if (pathname === \"/about\") return \"about\";\n        if (pathname === \"/contact\") return \"contact\";\n        return \"home\";\n    };\n    const activePage = getCurrentPage();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navbar.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"Navbar.useEffect.handleScroll\"];\n            window.addEventListener(\"scroll\", handleScroll);\n            return ({\n                \"Navbar.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    const handleNavigation = (page)=>{\n        if (onNavigate) {\n            onNavigate(page);\n        }\n        setIsOpen(false); // Close mobile menu after navigation\n        setActiveDropdown(null); // Close dropdown after navigation\n    };\n    const handleDropdownEnter = (dropdownName)=>{\n        setActiveDropdown(dropdownName);\n    };\n    const handleDropdownLeave = ()=>{\n        setActiveDropdown(null);\n    };\n    const navItems = [\n        {\n            name: \"Home\",\n            page: \"home\",\n            href: \"/\"\n        },\n        {\n            name: \"Projects\",\n            page: \"properties\",\n            href: \"/properties\"\n        },\n        {\n            name: \"Developers\",\n            page: \"developers\",\n            href: \"/developers\"\n        },\n        {\n            name: \"Services\",\n            page: \"services\",\n            hasDropdown: true\n        },\n        {\n            name: \"About\",\n            page: \"about\",\n            href: \"/about\"\n        },\n        {\n            name: \"Contact\",\n            page: \"contact\",\n            href: \"/contact\"\n        }\n    ];\n    const serviceItems = [\n        {\n            name: \"Company Formation\",\n            page: \"company-formation\",\n            href: \"/services/company-formation\"\n        },\n        {\n            name: \"Mortgages\",\n            page: \"mortgages\",\n            href: \"/services/mortgages\"\n        },\n        {\n            name: \"Golden Visa\",\n            page: \"golden-visa\",\n            href: \"/services/golden-visa\"\n        }\n    ];\n    const joinItems = [\n        {\n            name: \"Join As A Partner\",\n            page: \"join-as-partner\",\n            href: \"/join/partner\"\n        },\n        {\n            name: \"Become a Franchisee\",\n            page: \"become-franchisee\",\n            href: \"/join/franchise\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? \"bg-white/95 backdrop-blur-md shadow-[0_4px_20px_-2px_rgba(139,115,85,0.08),0_2px_8px_-2px_rgba(139,115,85,0.04)]\" : \"bg-white/90 backdrop-blur-sm\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-20\",\n                    children: [\n                        onLogoClick ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onLogoClick,\n                            className: \"hover:opacity-80 transition-opacity text-[16px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartOffPlanLogo__WEBPACK_IMPORTED_MODULE_5__.SmartOffPlanLogo, {\n                                className: \"h-12 w-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"hover:opacity-80 transition-opacity text-[16px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartOffPlanLogo__WEBPACK_IMPORTED_MODULE_5__.SmartOffPlanLogo, {\n                                className: \"h-12 w-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-1\",\n                            children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>handleDropdownEnter(item.name),\n                                        onMouseLeave: handleDropdownLeave,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"relative px-4 py-2 text-soft-brown hover:text-gold transition-all duration-300 group flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10 text-sm font-medium tracking-wide\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: `w-3 h-3 ml-1 transition-transform duration-300 ${activeDropdown === item.name ? \"rotate-180\" : \"group-hover:rotate-180\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `absolute inset-0 bg-beige/50 rounded-lg transition-transform duration-300 origin-center ${activeDropdown === item.name ? \"scale-100\" : \"scale-0 group-hover:scale-100\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `absolute bottom-0 left-1/2 h-0.5 bg-gold transition-all duration-300 ${activeDropdown === item.name ? \"w-6 -translate-x-1/2\" : \"w-0 group-hover:w-6 group-hover:left-1/2 group-hover:-translate-x-1/2\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `absolute top-full left-0 pt-1 w-64 z-50 transition-all duration-300 ${activeDropdown === item.name ? \"opacity-100 visible translate-y-0\" : \"opacity-0 invisible translate-y-2 pointer-events-none\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/95 backdrop-blur-md rounded-2xl shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] border border-gold/10 overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2\",\n                                                        children: serviceItems.map((service, serviceIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: service.href || \"/\",\n                                                                onClick: ()=>handleNavigation(service.page),\n                                                                className: \"w-full text-left px-4 py-3 text-sm text-soft-brown hover:text-gold hover:bg-beige/50 rounded-xl transition-all duration-300 flex items-center group/item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-3 text-gold group-hover/item:scale-110 transition-transform duration-200\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"group-hover/item:translate-x-1 transition-transform duration-200\",\n                                                                        children: service.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, service.name, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href || \"/\",\n                                        onClick: ()=>handleNavigation(item.page),\n                                        className: `relative px-4 py-2 transition-all duration-300 group ${activePage === item.page ? \"text-gold\" : \"text-soft-brown hover:text-gold\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 text-sm font-medium tracking-wide\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-beige/50 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300 origin-center\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `absolute bottom-0 left-1/2 h-0.5 bg-gold transition-all duration-300 ${activePage === item.page ? \"w-6 -translate-x-1/2\" : \"w-0 group-hover:w-6 group-hover:-translate-x-1/2\"}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                onMouseEnter: ()=>handleDropdownEnter(\"join-us\"),\n                                onMouseLeave: handleDropdownLeave,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        className: `bg-gold hover:bg-gold/90 text-soft-brown px-6 py-2 rounded-xl transition-all duration-300 hover:shadow-[0_4px_16px_-2px_rgba(212,175,55,0.3)] flex items-center ${activeDropdown === \"join-us\" ? \"scale-105 shadow-[0_4px_16px_-2px_rgba(212,175,55,0.3)]\" : \"hover:scale-105\"}`,\n                                        children: [\n                                            \"Join Us\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: `w-4 h-4 ml-2 transition-transform duration-300 ${activeDropdown === \"join-us\" ? \"rotate-180\" : \"\"}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `absolute top-full right-0 pt-1 w-56 z-50 transition-all duration-300 ${activeDropdown === \"join-us\" ? \"opacity-100 visible translate-y-0\" : \"opacity-0 invisible translate-y-2 pointer-events-none\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/95 backdrop-blur-md rounded-2xl shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] border border-gold/10 overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2\",\n                                                children: joinItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: item.href || \"/\",\n                                                        onClick: ()=>handleNavigation(item.page),\n                                                        className: \"w-full text-left px-4 py-3 text-sm text-soft-brown hover:text-gold hover:bg-beige/50 rounded-xl transition-all duration-300 flex items-center group/item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-3 text-gold group-hover/item:scale-110 transition-transform duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"group-hover/item:translate-x-1 transition-transform duration-200\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.name, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsOpen(!isOpen),\n                            className: \"lg:hidden w-10 h-10 flex items-center justify-center text-soft-brown hover:text-gold transition-colors duration-300\",\n                            children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 23\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 51\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden absolute top-full left-0 right-0 bg-white/95 backdrop-blur-md border-t border-gold/10 shadow-[0_8px_32px_-4px_rgba(139,115,85,0.12),0_4px_16px_-4px_rgba(139,115,85,0.08)] z-40\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container py-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-4 py-3 text-soft-brown text-sm font-medium border-b border-gold/10\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 25\n                                                }, this),\n                                                serviceItems.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: service.href || \"/\",\n                                                        onClick: ()=>handleNavigation(service.page),\n                                                        className: \"w-full text-left px-8 py-3 text-warm-gray hover:text-gold hover:bg-beige/50 transition-all duration-300 rounded-xl flex items-center group/mobile\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-3 text-gold group-hover/mobile:scale-110 transition-transform duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"group-hover/mobile:translate-x-1 transition-transform duration-200\",\n                                                                children: service.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, service.name, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href || \"/\",\n                                            onClick: ()=>handleNavigation(item.page),\n                                            className: `w-full text-left px-4 py-3 transition-all duration-300 rounded-xl ${activePage === item.page ? \"text-gold bg-beige/50\" : \"text-soft-brown hover:text-gold hover:bg-beige/50\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium tracking-wide\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t border-gold/10 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-4 py-3 text-soft-brown text-sm font-medium\",\n                                            children: \"Join Us\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, this),\n                                        joinItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href || \"/\",\n                                                onClick: ()=>handleNavigation(item.page),\n                                                className: \"w-full text-left px-8 py-3 text-warm-gray hover:text-gold hover:bg-beige/50 transition-all duration-300 rounded-xl flex items-center group/mobile\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-3 text-gold group-hover/mobile:scale-110 transition-transform duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"group-hover/mobile:translate-x-1 transition-transform duration-200\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 21\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\Navbar.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9OYXZiYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE0QztBQUNmO0FBQ2lCO0FBQ1Q7QUFDMkI7QUFDVjtBQVEvQyxTQUFTVSxPQUFPLEVBQUVDLFVBQVUsRUFBRUMsV0FBVyxFQUFFQyxXQUFXLEVBQWU7SUFDMUUsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdmLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQ2dCLFlBQVlDLGNBQWMsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2tCLGdCQUFnQkMsa0JBQWtCLEdBQUduQiwrQ0FBUUEsQ0FBZ0I7SUFDcEUsTUFBTW9CLFdBQVdqQiw0REFBV0E7SUFFNUIsdURBQXVEO0lBQ3ZELE1BQU1rQixpQkFBaUI7UUFDckIsSUFBSVIsYUFBYSxPQUFPQTtRQUN4QixJQUFJTyxhQUFhLEtBQUssT0FBTztRQUM3QixJQUFJQSxhQUFhLGVBQWUsT0FBTztRQUN2QyxJQUFJQSxhQUFhLGVBQWUsT0FBTztRQUN2QyxJQUFJQSxhQUFhLFVBQVUsT0FBTztRQUNsQyxJQUFJQSxhQUFhLFlBQVksT0FBTztRQUNwQyxPQUFPO0lBQ1Q7SUFFQSxNQUFNRSxhQUFhRDtJQUVuQnBCLGdEQUFTQTs0QkFBQztZQUNSLE1BQU1zQjtpREFBZTtvQkFDbkJOLGNBQWNPLE9BQU9DLE9BQU8sR0FBRztnQkFDakM7O1lBRUFELE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1lBQ2xDO29DQUFPLElBQU1DLE9BQU9HLG1CQUFtQixDQUFDLFVBQVVKOztRQUNwRDsyQkFBRyxFQUFFO0lBRUwsTUFBTUssbUJBQW1CLENBQUNDO1FBQ3hCLElBQUlsQixZQUFZO1lBQ2RBLFdBQVdrQjtRQUNiO1FBQ0FkLFVBQVUsUUFBUSxxQ0FBcUM7UUFDdkRJLGtCQUFrQixPQUFPLGtDQUFrQztJQUM3RDtJQUVBLE1BQU1XLHNCQUFzQixDQUFDQztRQUMzQlosa0JBQWtCWTtJQUNwQjtJQUVBLE1BQU1DLHNCQUFzQjtRQUMxQmIsa0JBQWtCO0lBQ3BCO0lBRUEsTUFBTWMsV0FBVztRQUNmO1lBQUVDLE1BQU07WUFBUUwsTUFBTTtZQUFRTSxNQUFNO1FBQUk7UUFDeEM7WUFBRUQsTUFBTTtZQUFZTCxNQUFNO1lBQWNNLE1BQU07UUFBYztRQUM1RDtZQUFFRCxNQUFNO1lBQWNMLE1BQU07WUFBY00sTUFBTTtRQUFjO1FBQzlEO1lBQUVELE1BQU07WUFBWUwsTUFBTTtZQUFZTyxhQUFhO1FBQUs7UUFDeEQ7WUFBRUYsTUFBTTtZQUFTTCxNQUFNO1lBQVNNLE1BQU07UUFBUztRQUMvQztZQUFFRCxNQUFNO1lBQVdMLE1BQU07WUFBV00sTUFBTTtRQUFXO0tBQ3REO0lBRUQsTUFBTUUsZUFBZTtRQUNuQjtZQUNFSCxNQUFNO1lBQ05MLE1BQU07WUFDTk0sTUFBTTtRQUNSO1FBQ0E7WUFBRUQsTUFBTTtZQUFhTCxNQUFNO1lBQWFNLE1BQU07UUFBc0I7UUFDcEU7WUFBRUQsTUFBTTtZQUFlTCxNQUFNO1lBQWVNLE1BQU07UUFBd0I7S0FDM0U7SUFFRCxNQUFNRyxZQUFZO1FBQ2hCO1lBQ0VKLE1BQU07WUFDTkwsTUFBTTtZQUNOTSxNQUFNO1FBQ1I7UUFDQTtZQUNFRCxNQUFNO1lBQ05MLE1BQU07WUFDTk0sTUFBTTtRQUNSO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQ0k7UUFDQ0MsV0FBVyxDQUFDLDREQUE0RCxFQUN0RXhCLGFBQ0kscUhBQ0EsZ0NBQ0o7a0JBRUYsNEVBQUN5QjtZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUlELFdBQVU7O3dCQUVaNUIsNEJBQ0MsOERBQUM4Qjs0QkFDQ0MsU0FBUy9COzRCQUNUNEIsV0FBVTtzQ0FFViw0RUFBQy9CLCtEQUFnQkE7Z0NBQUMrQixXQUFVOzs7Ozs7Ozs7O2lEQUc5Qiw4REFBQ3RDLGtEQUFJQTs0QkFDSGlDLE1BQUs7NEJBQ0xLLFdBQVU7c0NBRVYsNEVBQUMvQiwrREFBZ0JBO2dDQUFDK0IsV0FBVTs7Ozs7Ozs7Ozs7c0NBS2hDLDhEQUFDQzs0QkFBSUQsV0FBVTtzQ0FDWlAsU0FBU1csR0FBRyxDQUFDLENBQUNDLE1BQU1DLHNCQUNuQiw4REFBQ0w7b0NBQW9CRCxXQUFVOzhDQUM1QkssS0FBS1QsV0FBVyxpQkFDZiw4REFBQ0s7d0NBQ0NELFdBQVU7d0NBQ1ZPLGNBQWMsSUFBTWpCLG9CQUFvQmUsS0FBS1gsSUFBSTt3Q0FDakRjLGNBQWNoQjs7MERBRWQsOERBQUNVO2dEQUFPRixXQUFVOztrRUFDaEIsOERBQUNTO3dEQUFLVCxXQUFVO2tFQUNiSyxLQUFLWCxJQUFJOzs7Ozs7a0VBRVosOERBQUMxQix5R0FBV0E7d0RBQ1ZnQyxXQUFXLENBQUMsK0NBQStDLEVBQ3pEdEIsbUJBQW1CMkIsS0FBS1gsSUFBSSxHQUN4QixlQUNBLDBCQUNKOzs7Ozs7a0VBRUosOERBQUNPO3dEQUNDRCxXQUFXLENBQUMsd0ZBQXdGLEVBQ2xHdEIsbUJBQW1CMkIsS0FBS1gsSUFBSSxHQUN4QixjQUNBLGlDQUNKOzs7Ozs7a0VBRUosOERBQUNPO3dEQUNDRCxXQUFXLENBQUMscUVBQXFFLEVBQy9FdEIsbUJBQW1CMkIsS0FBS1gsSUFBSSxHQUN4Qix5QkFDQSx5RUFDSjs7Ozs7Ozs7Ozs7OzBEQUtOLDhEQUFDTztnREFDQ0QsV0FBVyxDQUFDLG9FQUFvRSxFQUM5RXRCLG1CQUFtQjJCLEtBQUtYLElBQUksR0FDeEIsc0NBQ0EseURBQ0o7MERBRUYsNEVBQUNPO29EQUFJRCxXQUFVOzhEQUNiLDRFQUFDQzt3REFBSUQsV0FBVTtrRUFDWkgsYUFBYU8sR0FBRyxDQUFDLENBQUNNLFNBQVNDLDZCQUMxQiw4REFBQ2pELGtEQUFJQTtnRUFFSGlDLE1BQU1lLFFBQVFmLElBQUksSUFBSTtnRUFDdEJRLFNBQVMsSUFBTWYsaUJBQWlCc0IsUUFBUXJCLElBQUk7Z0VBQzVDVyxXQUFVOztrRkFFViw4REFBQ2pDLHlHQUFVQTt3RUFBQ2lDLFdBQVU7Ozs7OztrRkFDdEIsOERBQUNTO3dFQUFLVCxXQUFVO2tGQUNiVSxRQUFRaEIsSUFBSTs7Ozs7OzsrREFQVmdCLFFBQVFoQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzZEQWdCN0IsOERBQUNoQyxrREFBSUE7d0NBQ0hpQyxNQUFNVSxLQUFLVixJQUFJLElBQUk7d0NBQ25CUSxTQUFTLElBQU1mLGlCQUFpQmlCLEtBQUtoQixJQUFJO3dDQUN6Q1csV0FBVyxDQUFDLHFEQUFxRCxFQUMvRGxCLGVBQWV1QixLQUFLaEIsSUFBSSxHQUNwQixjQUNBLG1DQUNKOzswREFFRiw4REFBQ29CO2dEQUFLVCxXQUFVOzBEQUNiSyxLQUFLWCxJQUFJOzs7Ozs7MERBRVosOERBQUNPO2dEQUFJRCxXQUFVOzs7Ozs7MERBQ2YsOERBQUNDO2dEQUNDRCxXQUFXLENBQUMscUVBQXFFLEVBQy9FbEIsZUFBZXVCLEtBQUtoQixJQUFJLEdBQ3BCLHlCQUNBLG9EQUNKOzs7Ozs7Ozs7Ozs7bUNBaEZBZ0IsS0FBS1gsSUFBSTs7Ozs7Ozs7OztzQ0F5RnZCLDhEQUFDTzs0QkFBSUQsV0FBVTtzQ0FDYiw0RUFBQ0M7Z0NBQ0NELFdBQVU7Z0NBQ1ZPLGNBQWMsSUFBTWpCLG9CQUFvQjtnQ0FDeENrQixjQUFjaEI7O2tEQUVkLDhEQUFDNUIsOENBQU1BO3dDQUNMb0MsV0FBVyxDQUFDLGdLQUFnSyxFQUMxS3RCLG1CQUFtQixZQUNmLDREQUNBLG1CQUNKOzs0Q0FDSDswREFFQyw4REFBQ1YseUdBQVdBO2dEQUNWZ0MsV0FBVyxDQUFDLCtDQUErQyxFQUN6RHRCLG1CQUFtQixZQUFZLGVBQWUsSUFDOUM7Ozs7Ozs7Ozs7OztrREFLTiw4REFBQ3VCO3dDQUNDRCxXQUFXLENBQUMscUVBQXFFLEVBQy9FdEIsbUJBQW1CLFlBQ2Ysc0NBQ0EseURBQ0o7a0RBRUYsNEVBQUN1Qjs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ0M7Z0RBQUlELFdBQVU7MERBQ1pGLFVBQVVNLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDcEIsOERBQUM1QyxrREFBSUE7d0RBRUhpQyxNQUFNVSxLQUFLVixJQUFJLElBQUk7d0RBQ25CUSxTQUFTLElBQU1mLGlCQUFpQmlCLEtBQUtoQixJQUFJO3dEQUN6Q1csV0FBVTs7MEVBRVYsOERBQUNqQyx5R0FBVUE7Z0VBQUNpQyxXQUFVOzs7Ozs7MEVBQ3RCLDhEQUFDUztnRUFBS1QsV0FBVTswRUFDYkssS0FBS1gsSUFBSTs7Ozs7Ozt1REFQUFcsS0FBS1gsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FrQjVCLDhEQUFDUTs0QkFDQ0MsU0FBUyxJQUFNNUIsVUFBVSxDQUFDRDs0QkFDMUIwQixXQUFVO3NDQUVUMUIsdUJBQVMsOERBQUNSLHlHQUFDQTtnQ0FBQ2tDLFdBQVU7Ozs7O3FEQUFlLDhEQUFDbkMseUdBQUlBO2dDQUFDbUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBS3pEMUIsd0JBQ0MsOERBQUMyQjtvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNDOzRCQUFJRCxXQUFVOztnQ0FDWlAsU0FBU1csR0FBRyxDQUFDLENBQUNDLE1BQU1DLHNCQUNuQiw4REFBQ0w7a0RBQ0VJLEtBQUtULFdBQVcsaUJBQ2YsOERBQUNLOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUlELFdBQVU7OERBQ1pLLEtBQUtYLElBQUk7Ozs7OztnREFFWEcsYUFBYU8sR0FBRyxDQUFDLENBQUNNLHdCQUNqQiw4REFBQ2hELGtEQUFJQTt3REFFSGlDLE1BQU1lLFFBQVFmLElBQUksSUFBSTt3REFDdEJRLFNBQVMsSUFBTWYsaUJBQWlCc0IsUUFBUXJCLElBQUk7d0RBQzVDVyxXQUFVOzswRUFFViw4REFBQ2pDLHlHQUFVQTtnRUFBQ2lDLFdBQVU7Ozs7OzswRUFDdEIsOERBQUNTO2dFQUFLVCxXQUFVOzBFQUNiVSxRQUFRaEIsSUFBSTs7Ozs7Ozt1REFQVmdCLFFBQVFoQixJQUFJOzs7Ozs7Ozs7O2lFQWF2Qiw4REFBQ2hDLGtEQUFJQTs0Q0FDSGlDLE1BQU1VLEtBQUtWLElBQUksSUFBSTs0Q0FDbkJRLFNBQVMsSUFBTWYsaUJBQWlCaUIsS0FBS2hCLElBQUk7NENBQ3pDVyxXQUFXLENBQUMsa0VBQWtFLEVBQzVFbEIsZUFBZXVCLEtBQUtoQixJQUFJLEdBQ3BCLDBCQUNBLHFEQUNKO3NEQUVGLDRFQUFDb0I7Z0RBQUtULFdBQVU7MERBQ2JLLEtBQUtYLElBQUk7Ozs7Ozs7Ozs7O3VDQS9CUlcsS0FBS1gsSUFBSTs7Ozs7OENBdUNyQiw4REFBQ087b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFBZ0Q7Ozs7Ozt3Q0FHOURGLFVBQVVNLEdBQUcsQ0FBQyxDQUFDQyxxQkFDZCw4REFBQzNDLGtEQUFJQTtnREFFSGlDLE1BQU1VLEtBQUtWLElBQUksSUFBSTtnREFDbkJRLFNBQVMsSUFBTWYsaUJBQWlCaUIsS0FBS2hCLElBQUk7Z0RBQ3pDVyxXQUFVOztrRUFFViw4REFBQ2pDLHlHQUFVQTt3REFBQ2lDLFdBQVU7Ozs7OztrRUFDdEIsOERBQUNTO3dEQUFLVCxXQUFVO2tFQUNiSyxLQUFLWCxJQUFJOzs7Ozs7OytDQVBQVyxLQUFLWCxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQW1CcEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmxhY2tcXERlc2t0b3BcXHNtYXJ0LW9mZi1wbGFuXFxhcHBcXGNvbXBvbmVudHNcXE5hdmJhci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xuaW1wb3J0IHsgdXNlUGF0aG5hbWUgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiLi91aS9idXR0b25cIjtcbmltcG9ydCB7IE1lbnUsIFgsIFRyZW5kaW5nVXAsIENoZXZyb25Eb3duIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IHsgU21hcnRPZmZQbGFuTG9nbyB9IGZyb20gXCIuL1NtYXJ0T2ZmUGxhbkxvZ29cIjtcblxuaW50ZXJmYWNlIE5hdmJhclByb3BzIHtcbiAgb25OYXZpZ2F0ZT86IChwYWdlOiBzdHJpbmcpID0+IHZvaWQ7XG4gIG9uTG9nb0NsaWNrPzogKCkgPT4gdm9pZDtcbiAgY3VycmVudFBhZ2U/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBOYXZiYXIoeyBvbk5hdmlnYXRlLCBvbkxvZ29DbGljaywgY3VycmVudFBhZ2UgfTogTmF2YmFyUHJvcHMpIHtcbiAgY29uc3QgW2lzT3Blbiwgc2V0SXNPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzU2Nyb2xsZWQsIHNldElzU2Nyb2xsZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbYWN0aXZlRHJvcGRvd24sIHNldEFjdGl2ZURyb3Bkb3duXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKCk7XG5cbiAgLy8gRGV0ZXJtaW5lIGN1cnJlbnQgcGFnZSBmcm9tIHBhdGhuYW1lIGlmIG5vdCBwcm92aWRlZFxuICBjb25zdCBnZXRDdXJyZW50UGFnZSA9ICgpID0+IHtcbiAgICBpZiAoY3VycmVudFBhZ2UpIHJldHVybiBjdXJyZW50UGFnZTtcbiAgICBpZiAocGF0aG5hbWUgPT09IFwiL1wiKSByZXR1cm4gXCJob21lXCI7XG4gICAgaWYgKHBhdGhuYW1lID09PSBcIi9wcm9wZXJ0aWVzXCIpIHJldHVybiBcInByb3BlcnRpZXNcIjtcbiAgICBpZiAocGF0aG5hbWUgPT09IFwiL2RldmVsb3BlcnNcIikgcmV0dXJuIFwiZGV2ZWxvcGVyc1wiO1xuICAgIGlmIChwYXRobmFtZSA9PT0gXCIvYWJvdXRcIikgcmV0dXJuIFwiYWJvdXRcIjtcbiAgICBpZiAocGF0aG5hbWUgPT09IFwiL2NvbnRhY3RcIikgcmV0dXJuIFwiY29udGFjdFwiO1xuICAgIHJldHVybiBcImhvbWVcIjtcbiAgfTtcblxuICBjb25zdCBhY3RpdmVQYWdlID0gZ2V0Q3VycmVudFBhZ2UoKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZVNjcm9sbCA9ICgpID0+IHtcbiAgICAgIHNldElzU2Nyb2xsZWQod2luZG93LnNjcm9sbFkgPiA1MCk7XG4gICAgfTtcblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwic2Nyb2xsXCIsIGhhbmRsZVNjcm9sbCk7XG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwic2Nyb2xsXCIsIGhhbmRsZVNjcm9sbCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBoYW5kbGVOYXZpZ2F0aW9uID0gKHBhZ2U6IHN0cmluZykgPT4ge1xuICAgIGlmIChvbk5hdmlnYXRlKSB7XG4gICAgICBvbk5hdmlnYXRlKHBhZ2UpO1xuICAgIH1cbiAgICBzZXRJc09wZW4oZmFsc2UpOyAvLyBDbG9zZSBtb2JpbGUgbWVudSBhZnRlciBuYXZpZ2F0aW9uXG4gICAgc2V0QWN0aXZlRHJvcGRvd24obnVsbCk7IC8vIENsb3NlIGRyb3Bkb3duIGFmdGVyIG5hdmlnYXRpb25cbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcm9wZG93bkVudGVyID0gKGRyb3Bkb3duTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0QWN0aXZlRHJvcGRvd24oZHJvcGRvd25OYW1lKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcm9wZG93bkxlYXZlID0gKCkgPT4ge1xuICAgIHNldEFjdGl2ZURyb3Bkb3duKG51bGwpO1xuICB9O1xuXG4gIGNvbnN0IG5hdkl0ZW1zID0gW1xuICAgIHsgbmFtZTogXCJIb21lXCIsIHBhZ2U6IFwiaG9tZVwiLCBocmVmOiBcIi9cIiB9LFxuICAgIHsgbmFtZTogXCJQcm9qZWN0c1wiLCBwYWdlOiBcInByb3BlcnRpZXNcIiwgaHJlZjogXCIvcHJvcGVydGllc1wiIH0sXG4gICAgeyBuYW1lOiBcIkRldmVsb3BlcnNcIiwgcGFnZTogXCJkZXZlbG9wZXJzXCIsIGhyZWY6IFwiL2RldmVsb3BlcnNcIiB9LFxuICAgIHsgbmFtZTogXCJTZXJ2aWNlc1wiLCBwYWdlOiBcInNlcnZpY2VzXCIsIGhhc0Ryb3Bkb3duOiB0cnVlIH0sXG4gICAgeyBuYW1lOiBcIkFib3V0XCIsIHBhZ2U6IFwiYWJvdXRcIiwgaHJlZjogXCIvYWJvdXRcIiB9LFxuICAgIHsgbmFtZTogXCJDb250YWN0XCIsIHBhZ2U6IFwiY29udGFjdFwiLCBocmVmOiBcIi9jb250YWN0XCIgfSxcbiAgXTtcblxuICBjb25zdCBzZXJ2aWNlSXRlbXMgPSBbXG4gICAge1xuICAgICAgbmFtZTogXCJDb21wYW55IEZvcm1hdGlvblwiLFxuICAgICAgcGFnZTogXCJjb21wYW55LWZvcm1hdGlvblwiLFxuICAgICAgaHJlZjogXCIvc2VydmljZXMvY29tcGFueS1mb3JtYXRpb25cIixcbiAgICB9LFxuICAgIHsgbmFtZTogXCJNb3J0Z2FnZXNcIiwgcGFnZTogXCJtb3J0Z2FnZXNcIiwgaHJlZjogXCIvc2VydmljZXMvbW9ydGdhZ2VzXCIgfSxcbiAgICB7IG5hbWU6IFwiR29sZGVuIFZpc2FcIiwgcGFnZTogXCJnb2xkZW4tdmlzYVwiLCBocmVmOiBcIi9zZXJ2aWNlcy9nb2xkZW4tdmlzYVwiIH0sXG4gIF07XG5cbiAgY29uc3Qgam9pbkl0ZW1zID0gW1xuICAgIHtcbiAgICAgIG5hbWU6IFwiSm9pbiBBcyBBIFBhcnRuZXJcIixcbiAgICAgIHBhZ2U6IFwiam9pbi1hcy1wYXJ0bmVyXCIsXG4gICAgICBocmVmOiBcIi9qb2luL3BhcnRuZXJcIixcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6IFwiQmVjb21lIGEgRnJhbmNoaXNlZVwiLFxuICAgICAgcGFnZTogXCJiZWNvbWUtZnJhbmNoaXNlZVwiLFxuICAgICAgaHJlZjogXCIvam9pbi9mcmFuY2hpc2VcIixcbiAgICB9LFxuICBdO1xuXG4gIHJldHVybiAoXG4gICAgPG5hdlxuICAgICAgY2xhc3NOYW1lPXtgZml4ZWQgdG9wLTAgbGVmdC0wIHJpZ2h0LTAgei01MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgaXNTY3JvbGxlZFxuICAgICAgICAgID8gXCJiZy13aGl0ZS85NSBiYWNrZHJvcC1ibHVyLW1kIHNoYWRvdy1bMF80cHhfMjBweF8tMnB4X3JnYmEoMTM5LDExNSw4NSwwLjA4KSwwXzJweF84cHhfLTJweF9yZ2JhKDEzOSwxMTUsODUsMC4wNCldXCJcbiAgICAgICAgICA6IFwiYmctd2hpdGUvOTAgYmFja2Ryb3AtYmx1ci1zbVwiXG4gICAgICB9YH1cbiAgICA+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBoLTIwXCI+XG4gICAgICAgICAgey8qIExvZ28gT25seSAqL31cbiAgICAgICAgICB7b25Mb2dvQ2xpY2sgPyAoXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uTG9nb0NsaWNrfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3ZlcjpvcGFjaXR5LTgwIHRyYW5zaXRpb24tb3BhY2l0eSB0ZXh0LVsxNnB4XVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxTbWFydE9mZlBsYW5Mb2dvIGNsYXNzTmFtZT1cImgtMTIgdy1hdXRvXCIgLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBocmVmPVwiL1wiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhvdmVyOm9wYWNpdHktODAgdHJhbnNpdGlvbi1vcGFjaXR5IHRleHQtWzE2cHhdXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFNtYXJ0T2ZmUGxhbkxvZ28gY2xhc3NOYW1lPVwiaC0xMiB3LWF1dG9cIiAvPlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogRGVza3RvcCBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAge25hdkl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e2l0ZW0ubmFtZX0gY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICB7aXRlbS5oYXNEcm9wZG93biA/IChcbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmVcIlxuICAgICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IGhhbmRsZURyb3Bkb3duRW50ZXIoaXRlbS5uYW1lKX1cbiAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXtoYW5kbGVEcm9wZG93bkxlYXZlfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInJlbGF0aXZlIHB4LTQgcHktMiB0ZXh0LXNvZnQtYnJvd24gaG92ZXI6dGV4dC1nb2xkIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBncm91cCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFja2luZy13aWRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd25cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctMyBoLTMgbWwtMSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlRHJvcGRvd24gPT09IGl0ZW0ubmFtZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJyb3RhdGUtMTgwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiZ3JvdXAtaG92ZXI6cm90YXRlLTE4MFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGFic29sdXRlIGluc2V0LTAgYmctYmVpZ2UvNTAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgb3JpZ2luLWNlbnRlciAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93biA9PT0gaXRlbS5uYW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInNjYWxlLTEwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInNjYWxlLTAgZ3JvdXAtaG92ZXI6c2NhbGUtMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgYm90dG9tLTAgbGVmdC0xLzIgaC0wLjUgYmctZ29sZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlRHJvcGRvd24gPT09IGl0ZW0ubmFtZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ3LTYgLXRyYW5zbGF0ZS14LTEvMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInctMCBncm91cC1ob3Zlcjp3LTYgZ3JvdXAtaG92ZXI6bGVmdC0xLzIgZ3JvdXAtaG92ZXI6LXRyYW5zbGF0ZS14LTEvMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogU2VydmljZXMgRHJvcGRvd24gKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSB0b3AtZnVsbCBsZWZ0LTAgcHQtMSB3LTY0IHotNTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93biA9PT0gaXRlbS5uYW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJvcGFjaXR5LTEwMCB2aXNpYmxlIHRyYW5zbGF0ZS15LTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwib3BhY2l0eS0wIGludmlzaWJsZSB0cmFuc2xhdGUteS0yIHBvaW50ZXItZXZlbnRzLW5vbmVcIlxuICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS85NSBiYWNrZHJvcC1ibHVyLW1kIHJvdW5kZWQtMnhsIHNoYWRvdy1bMF84cHhfMzJweF8tNHB4X3JnYmEoMTM5LDExNSw4NSwwLjEyKSwwXzRweF8xNnB4Xy00cHhfcmdiYSgxMzksMTE1LDg1LDAuMDgpXSBib3JkZXIgYm9yZGVyLWdvbGQvMTAgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7c2VydmljZUl0ZW1zLm1hcCgoc2VydmljZSwgc2VydmljZUluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17c2VydmljZS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17c2VydmljZS5ocmVmIHx8IFwiL1wifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTmF2aWdhdGlvbihzZXJ2aWNlLnBhZ2UpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtbGVmdCBweC00IHB5LTMgdGV4dC1zbSB0ZXh0LXNvZnQtYnJvd24gaG92ZXI6dGV4dC1nb2xkIGhvdmVyOmJnLWJlaWdlLzUwIHJvdW5kZWQteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGZsZXggaXRlbXMtY2VudGVyIGdyb3VwL2l0ZW1cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMyB0ZXh0LWdvbGQgZ3JvdXAtaG92ZXIvaXRlbTpzY2FsZS0xMTAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImdyb3VwLWhvdmVyL2l0ZW06dHJhbnNsYXRlLXgtMSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NlcnZpY2UubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZiB8fCBcIi9cIn1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTmF2aWdhdGlvbihpdGVtLnBhZ2UpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSBweC00IHB5LTIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGdyb3VwICR7XG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZlUGFnZSA9PT0gaXRlbS5wYWdlXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1nb2xkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXNvZnQtYnJvd24gaG92ZXI6dGV4dC1nb2xkXCJcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFja2luZy13aWRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctYmVpZ2UvNTAgcm91bmRlZC1sZyBzY2FsZS0wIGdyb3VwLWhvdmVyOnNjYWxlLTEwMCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgb3JpZ2luLWNlbnRlclwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSBib3R0b20tMCBsZWZ0LTEvMiBoLTAuNSBiZy1nb2xkIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlUGFnZSA9PT0gaXRlbS5wYWdlXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ3LTYgLXRyYW5zbGF0ZS14LTEvMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ3LTAgZ3JvdXAtaG92ZXI6dy02IGdyb3VwLWhvdmVyOi10cmFuc2xhdGUteC0xLzJcIlxuICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQ1RBIEJ1dHRvbiB3aXRoIERyb3Bkb3duICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiXG4gICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KCkgPT4gaGFuZGxlRHJvcGRvd25FbnRlcihcImpvaW4tdXNcIil9XG4gICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17aGFuZGxlRHJvcGRvd25MZWF2ZX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGJnLWdvbGQgaG92ZXI6YmctZ29sZC85MCB0ZXh0LXNvZnQtYnJvd24gcHgtNiBweS0yIHJvdW5kZWQteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNoYWRvdy1bMF80cHhfMTZweF8tMnB4X3JnYmEoMjEyLDE3NSw1NSwwLjMpXSBmbGV4IGl0ZW1zLWNlbnRlciAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlRHJvcGRvd24gPT09IFwiam9pbi11c1wiXG4gICAgICAgICAgICAgICAgICAgID8gXCJzY2FsZS0xMDUgc2hhZG93LVswXzRweF8xNnB4Xy0ycHhfcmdiYSgyMTIsMTc1LDU1LDAuMyldXCJcbiAgICAgICAgICAgICAgICAgICAgOiBcImhvdmVyOnNjYWxlLTEwNVwiXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBKb2luIFVzXG4gICAgICAgICAgICAgICAgPENoZXZyb25Eb3duXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTQgaC00IG1sLTIgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duID09PSBcImpvaW4tdXNcIiA/IFwicm90YXRlLTE4MFwiIDogXCJcIlxuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgICAgey8qIEpvaW4gVXMgRHJvcGRvd24gKi99XG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSB0b3AtZnVsbCByaWdodC0wIHB0LTEgdy01NiB6LTUwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlRHJvcGRvd24gPT09IFwiam9pbi11c1wiXG4gICAgICAgICAgICAgICAgICAgID8gXCJvcGFjaXR5LTEwMCB2aXNpYmxlIHRyYW5zbGF0ZS15LTBcIlxuICAgICAgICAgICAgICAgICAgICA6IFwib3BhY2l0eS0wIGludmlzaWJsZSB0cmFuc2xhdGUteS0yIHBvaW50ZXItZXZlbnRzLW5vbmVcIlxuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS85NSBiYWNrZHJvcC1ibHVyLW1kIHJvdW5kZWQtMnhsIHNoYWRvdy1bMF84cHhfMzJweF8tNHB4X3JnYmEoMTM5LDExNSw4NSwwLjEyKSwwXzRweF8xNnB4Xy00cHhfcmdiYSgxMzksMTE1LDg1LDAuMDgpXSBib3JkZXIgYm9yZGVyLWdvbGQvMTAgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMlwiPlxuICAgICAgICAgICAgICAgICAgICB7am9pbkl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWYgfHwgXCIvXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVOYXZpZ2F0aW9uKGl0ZW0ucGFnZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1sZWZ0IHB4LTQgcHktMyB0ZXh0LXNtIHRleHQtc29mdC1icm93biBob3Zlcjp0ZXh0LWdvbGQgaG92ZXI6YmctYmVpZ2UvNTAgcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZmxleCBpdGVtcy1jZW50ZXIgZ3JvdXAvaXRlbVwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwidy00IGgtNCBtci0zIHRleHQtZ29sZCBncm91cC1ob3Zlci9pdGVtOnNjYWxlLTExMCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0yMDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZ3JvdXAtaG92ZXIvaXRlbTp0cmFuc2xhdGUteC0xIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBNb2JpbGUgTWVudSBCdXR0b24gKi99XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNPcGVuKCFpc09wZW4pfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibGc6aGlkZGVuIHctMTAgaC0xMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXNvZnQtYnJvd24gaG92ZXI6dGV4dC1nb2xkIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzT3BlbiA/IDxYIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPiA6IDxNZW51IGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPn1cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE1vYmlsZSBOYXZpZ2F0aW9uICovfVxuICAgICAgICB7aXNPcGVuICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmhpZGRlbiBhYnNvbHV0ZSB0b3AtZnVsbCBsZWZ0LTAgcmlnaHQtMCBiZy13aGl0ZS85NSBiYWNrZHJvcC1ibHVyLW1kIGJvcmRlci10IGJvcmRlci1nb2xkLzEwIHNoYWRvdy1bMF84cHhfMzJweF8tNHB4X3JnYmEoMTM5LDExNSw4NSwwLjEyKSwwXzRweF8xNnB4Xy00cHhfcmdiYSgxMzksMTE1LDg1LDAuMDgpXSB6LTQwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBweS02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAge25hdkl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpdGVtLm5hbWV9PlxuICAgICAgICAgICAgICAgICAgICB7aXRlbS5oYXNEcm9wZG93biA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHB5LTMgdGV4dC1zb2Z0LWJyb3duIHRleHQtc20gZm9udC1tZWRpdW0gYm9yZGVyLWIgYm9yZGVyLWdvbGQvMTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAge3NlcnZpY2VJdGVtcy5tYXAoKHNlcnZpY2UpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3NlcnZpY2UubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtzZXJ2aWNlLmhyZWYgfHwgXCIvXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTmF2aWdhdGlvbihzZXJ2aWNlLnBhZ2UpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWxlZnQgcHgtOCBweS0zIHRleHQtd2FybS1ncmF5IGhvdmVyOnRleHQtZ29sZCBob3ZlcjpiZy1iZWlnZS81MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgcm91bmRlZC14bCBmbGV4IGl0ZW1zLWNlbnRlciBncm91cC9tb2JpbGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwidy00IGgtNCBtci0zIHRleHQtZ29sZCBncm91cC1ob3Zlci9tb2JpbGU6c2NhbGUtMTEwIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZ3JvdXAtaG92ZXIvbW9iaWxlOnRyYW5zbGF0ZS14LTEgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2VydmljZS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZiB8fCBcIi9cIn1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU5hdmlnYXRpb24oaXRlbS5wYWdlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCB0ZXh0LWxlZnQgcHgtNCBweS0zIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCByb3VuZGVkLXhsICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVBhZ2UgPT09IGl0ZW0ucGFnZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LWdvbGQgYmctYmVpZ2UvNTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXNvZnQtYnJvd24gaG92ZXI6dGV4dC1nb2xkIGhvdmVyOmJnLWJlaWdlLzUwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdHJhY2tpbmctd2lkZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuXG4gICAgICAgICAgICAgICAgey8qIE1vYmlsZSBKb2luIE9wdGlvbnMgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC00IGJvcmRlci10IGJvcmRlci1nb2xkLzEwIHNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHB5LTMgdGV4dC1zb2Z0LWJyb3duIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgSm9pbiBVc1xuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICB7am9pbkl0ZW1zLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZiB8fCBcIi9cIn1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVOYXZpZ2F0aW9uKGl0ZW0ucGFnZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtbGVmdCBweC04IHB5LTMgdGV4dC13YXJtLWdyYXkgaG92ZXI6dGV4dC1nb2xkIGhvdmVyOmJnLWJlaWdlLzUwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCByb3VuZGVkLXhsIGZsZXggaXRlbXMtY2VudGVyIGdyb3VwL21vYmlsZVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTMgdGV4dC1nb2xkIGdyb3VwLWhvdmVyL21vYmlsZTpzY2FsZS0xMTAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJncm91cC1ob3Zlci9tb2JpbGU6dHJhbnNsYXRlLXgtMSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvbmF2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiTGluayIsInVzZVBhdGhuYW1lIiwiQnV0dG9uIiwiTWVudSIsIlgiLCJUcmVuZGluZ1VwIiwiQ2hldnJvbkRvd24iLCJTbWFydE9mZlBsYW5Mb2dvIiwiTmF2YmFyIiwib25OYXZpZ2F0ZSIsIm9uTG9nb0NsaWNrIiwiY3VycmVudFBhZ2UiLCJpc09wZW4iLCJzZXRJc09wZW4iLCJpc1Njcm9sbGVkIiwic2V0SXNTY3JvbGxlZCIsImFjdGl2ZURyb3Bkb3duIiwic2V0QWN0aXZlRHJvcGRvd24iLCJwYXRobmFtZSIsImdldEN1cnJlbnRQYWdlIiwiYWN0aXZlUGFnZSIsImhhbmRsZVNjcm9sbCIsIndpbmRvdyIsInNjcm9sbFkiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImhhbmRsZU5hdmlnYXRpb24iLCJwYWdlIiwiaGFuZGxlRHJvcGRvd25FbnRlciIsImRyb3Bkb3duTmFtZSIsImhhbmRsZURyb3Bkb3duTGVhdmUiLCJuYXZJdGVtcyIsIm5hbWUiLCJocmVmIiwiaGFzRHJvcGRvd24iLCJzZXJ2aWNlSXRlbXMiLCJqb2luSXRlbXMiLCJuYXYiLCJjbGFzc05hbWUiLCJkaXYiLCJidXR0b24iLCJvbkNsaWNrIiwibWFwIiwiaXRlbSIsImluZGV4Iiwib25Nb3VzZUVudGVyIiwib25Nb3VzZUxlYXZlIiwic3BhbiIsInNlcnZpY2UiLCJzZXJ2aWNlSW5kZXgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/SmartOffPlanLogo.tsx":
/*!*********************************************!*\
  !*** ./app/components/SmartOffPlanLogo.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartOffPlanLogo: () => (/* binding */ SmartOffPlanLogo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction SmartOffPlanLogo({ className = \"h-10 w-auto\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        viewBox: \"0 0 400 80\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"40\",\n                cy: \"40\",\n                r: \"30\",\n                stroke: \"#d4af37\",\n                strokeWidth: \"0\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\SmartOffPlanLogo.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                x: \"85\",\n                y: \"32\",\n                fontFamily: \"Playfair Display, serif\",\n                fontSize: \"20\",\n                fontWeight: \"600\",\n                fill: \"#8b7355\",\n                letterSpacing: \"1.5px\",\n                children: \"SMART OFF PLAN\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\SmartOffPlanLogo.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                x: \"85\",\n                y: \"52\",\n                fontFamily: \"Inter, sans-serif\",\n                fontSize: \"10\",\n                fontWeight: \"400\",\n                fill: \"#8a7968\",\n                letterSpacing: \"3px\",\n                children: \"INVEST SMART\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\SmartOffPlanLogo.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\SmartOffPlanLogo.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/SmartOffPlanLogo.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./app/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./app/components/ui/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9 rounded-md\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/card.tsx":
/*!************************************!*\
  !*** ./app/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./app/components/ui/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pt-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6 [&:last-child]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 pb-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy91aS9jYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQStCO0FBRUY7QUFFN0IsU0FBU0UsS0FBSyxFQUFFQyxTQUFTLEVBQUUsR0FBR0MsT0FBb0M7SUFDaEUscUJBQ0UsOERBQUNDO1FBQ0NDLGFBQVU7UUFDVkgsV0FBV0YsMENBQUVBLENBQ1gsc0VBQ0FFO1FBRUQsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFQSxTQUFTRyxXQUFXLEVBQUVKLFNBQVMsRUFBRSxHQUFHQyxPQUFvQztJQUN0RSxxQkFDRSw4REFBQ0M7UUFDQ0MsYUFBVTtRQUNWSCxXQUFXRiwwQ0FBRUEsQ0FDWCxtS0FDQUU7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHZjtBQUVBLFNBQVNJLFVBQVUsRUFBRUwsU0FBUyxFQUFFLEdBQUdDLE9BQW9DO0lBQ3JFLHFCQUNFLDhEQUFDSztRQUNDSCxhQUFVO1FBQ1ZILFdBQVdGLDBDQUFFQSxDQUFDLGdCQUFnQkU7UUFDN0IsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFQSxTQUFTTSxnQkFBZ0IsRUFBRVAsU0FBUyxFQUFFLEdBQUdDLE9BQW9DO0lBQzNFLHFCQUNFLDhEQUFDTztRQUNDTCxhQUFVO1FBQ1ZILFdBQVdGLDBDQUFFQSxDQUFDLHlCQUF5QkU7UUFDdEMsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFQSxTQUFTUSxXQUFXLEVBQUVULFNBQVMsRUFBRSxHQUFHQyxPQUFvQztJQUN0RSxxQkFDRSw4REFBQ0M7UUFDQ0MsYUFBVTtRQUNWSCxXQUFXRiwwQ0FBRUEsQ0FDWCxrRUFDQUU7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHZjtBQUVBLFNBQVNTLFlBQVksRUFBRVYsU0FBUyxFQUFFLEdBQUdDLE9BQW9DO0lBQ3ZFLHFCQUNFLDhEQUFDQztRQUNDQyxhQUFVO1FBQ1ZILFdBQVdGLDBDQUFFQSxDQUFDLDRCQUE0QkU7UUFDekMsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFQSxTQUFTVSxXQUFXLEVBQUVYLFNBQVMsRUFBRSxHQUFHQyxPQUFvQztJQUN0RSxxQkFDRSw4REFBQ0M7UUFDQ0MsYUFBVTtRQUNWSCxXQUFXRiwwQ0FBRUEsQ0FBQyxnREFBZ0RFO1FBQzdELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBVUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmxhY2tcXERlc2t0b3BcXHNtYXJ0LW9mZi1wbGFuXFxhcHBcXGNvbXBvbmVudHNcXHVpXFxjYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiLi91dGlsc1wiO1xuXG5mdW5jdGlvbiBDYXJkKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGRhdGEtc2xvdD1cImNhcmRcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJiZy1jYXJkIHRleHQtY2FyZC1mb3JlZ3JvdW5kIGZsZXggZmxleC1jb2wgZ2FwLTYgcm91bmRlZC14bCBib3JkZXJcIixcbiAgICAgICAgY2xhc3NOYW1lLFxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApO1xufVxuXG5mdW5jdGlvbiBDYXJkSGVhZGVyKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGRhdGEtc2xvdD1cImNhcmQtaGVhZGVyXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiQGNvbnRhaW5lci9jYXJkLWhlYWRlciBncmlkIGF1dG8tcm93cy1taW4gZ3JpZC1yb3dzLVthdXRvX2F1dG9dIGl0ZW1zLXN0YXJ0IGdhcC0xLjUgcHgtNiBwdC02IGhhcy1kYXRhLVtzbG90PWNhcmQtYWN0aW9uXTpncmlkLWNvbHMtWzFmcl9hdXRvXSBbLmJvcmRlci1iXTpwYi02XCIsXG4gICAgICAgIGNsYXNzTmFtZSxcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKTtcbn1cblxuZnVuY3Rpb24gQ2FyZFRpdGxlKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxoNFxuICAgICAgZGF0YS1zbG90PVwiY2FyZC10aXRsZVwiXG4gICAgICBjbGFzc05hbWU9e2NuKFwibGVhZGluZy1ub25lXCIsIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKTtcbn1cblxuZnVuY3Rpb24gQ2FyZERlc2NyaXB0aW9uKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxwXG4gICAgICBkYXRhLXNsb3Q9XCJjYXJkLWRlc2NyaXB0aW9uXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIiwgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApO1xufVxuXG5mdW5jdGlvbiBDYXJkQWN0aW9uKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGRhdGEtc2xvdD1cImNhcmQtYWN0aW9uXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiY29sLXN0YXJ0LTIgcm93LXNwYW4tMiByb3ctc3RhcnQtMSBzZWxmLXN0YXJ0IGp1c3RpZnktc2VsZi1lbmRcIixcbiAgICAgICAgY2xhc3NOYW1lLFxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApO1xufVxuXG5mdW5jdGlvbiBDYXJkQ29udGVudCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfTogUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJkaXZcIj4pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICBkYXRhLXNsb3Q9XCJjYXJkLWNvbnRlbnRcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcInB4LTYgWyY6bGFzdC1jaGlsZF06cGItNlwiLCBjbGFzc05hbWUpfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gICk7XG59XG5cbmZ1bmN0aW9uIENhcmRGb290ZXIoeyBjbGFzc05hbWUsIC4uLnByb3BzIH06IFJlYWN0LkNvbXBvbmVudFByb3BzPFwiZGl2XCI+KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgZGF0YS1zbG90PVwiY2FyZC1mb290ZXJcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcImZsZXggaXRlbXMtY2VudGVyIHB4LTYgcGItNiBbLmJvcmRlci10XTpwdC02XCIsIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKTtcbn1cblxuZXhwb3J0IHtcbiAgQ2FyZCxcbiAgQ2FyZEhlYWRlcixcbiAgQ2FyZEZvb3RlcixcbiAgQ2FyZFRpdGxlLFxuICBDYXJkQWN0aW9uLFxuICBDYXJkRGVzY3JpcHRpb24sXG4gIENhcmRDb250ZW50LFxufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiQ2FyZCIsImNsYXNzTmFtZSIsInByb3BzIiwiZGl2IiwiZGF0YS1zbG90IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsImg0IiwiQ2FyZERlc2NyaXB0aW9uIiwicCIsIkNhcmRBY3Rpb24iLCJDYXJkQ29udGVudCIsIkNhcmRGb290ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./app/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./app/components/ui/utils.ts\");\n\n\n\nfunction Input({ className, type, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        \"data-slot\": \"input\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border px-3 py-1 text-base bg-input-background transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUErQjtBQUVGO0FBRTdCLFNBQVNFLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxJQUFJLEVBQUUsR0FBR0MsT0FBc0M7SUFDekUscUJBQ0UsOERBQUNDO1FBQ0NGLE1BQU1BO1FBQ05HLGFBQVU7UUFDVkosV0FBV0YsMENBQUVBLENBQ1gsOGJBQ0EsaUZBQ0EsMEdBQ0FFO1FBRUQsR0FBR0UsS0FBSzs7Ozs7O0FBR2Y7QUFFaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmxhY2tcXERlc2t0b3BcXHNtYXJ0LW9mZi1wbGFuXFxhcHBcXGNvbXBvbmVudHNcXHVpXFxpbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIi4vdXRpbHNcIjtcblxuZnVuY3Rpb24gSW5wdXQoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH06IFJlYWN0LkNvbXBvbmVudFByb3BzPFwiaW5wdXRcIj4pIHtcbiAgcmV0dXJuIChcbiAgICA8aW5wdXRcbiAgICAgIHR5cGU9e3R5cGV9XG4gICAgICBkYXRhLXNsb3Q9XCJpbnB1dFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBzZWxlY3Rpb246YmctcHJpbWFyeSBzZWxlY3Rpb246dGV4dC1wcmltYXJ5LWZvcmVncm91bmQgZGFyazpiZy1pbnB1dC8zMCBib3JkZXItaW5wdXQgZmxleCBoLTkgdy1mdWxsIG1pbi13LTAgcm91bmRlZC1tZCBib3JkZXIgcHgtMyBweS0xIHRleHQtYmFzZSBiZy1pbnB1dC1iYWNrZ3JvdW5kIHRyYW5zaXRpb24tW2NvbG9yLGJveC1zaGFkb3ddIG91dGxpbmUtbm9uZSBmaWxlOmlubGluZS1mbGV4IGZpbGU6aC03IGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwIG1kOnRleHQtc21cIixcbiAgICAgICAgXCJmb2N1cy12aXNpYmxlOmJvcmRlci1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1yaW5nLzUwIGZvY3VzLXZpc2libGU6cmluZy1bM3B4XVwiLFxuICAgICAgICBcImFyaWEtaW52YWxpZDpyaW5nLWRlc3RydWN0aXZlLzIwIGRhcms6YXJpYS1pbnZhbGlkOnJpbmctZGVzdHJ1Y3RpdmUvNDAgYXJpYS1pbnZhbGlkOmJvcmRlci1kZXN0cnVjdGl2ZVwiLFxuICAgICAgICBjbGFzc05hbWUsXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gICk7XG59XG5cbmV4cG9ydCB7IElucHV0IH07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwiaW5wdXQiLCJkYXRhLXNsb3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/textarea.tsx":
/*!****************************************!*\
  !*** ./app/components/ui/textarea.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./app/components/ui/utils.ts\");\n\n\n\nfunction Textarea({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        \"data-slot\": \"textarea\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"resize-none border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-input-background px-3 py-2 text-base transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy91aS90ZXh0YXJlYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUErQjtBQUVGO0FBRTdCLFNBQVNFLFNBQVMsRUFBRUMsU0FBUyxFQUFFLEdBQUdDLE9BQXlDO0lBQ3pFLHFCQUNFLDhEQUFDQztRQUNDQyxhQUFVO1FBQ1ZILFdBQVdGLDBDQUFFQSxDQUNYLDhjQUNBRTtRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBRW9CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEJsYWNrXFxEZXNrdG9wXFxzbWFydC1vZmYtcGxhblxcYXBwXFxjb21wb25lbnRzXFx1aVxcdGV4dGFyZWEudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCIuL3V0aWxzXCI7XG5cbmZ1bmN0aW9uIFRleHRhcmVhKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcInRleHRhcmVhXCI+KSB7XG4gIHJldHVybiAoXG4gICAgPHRleHRhcmVhXG4gICAgICBkYXRhLXNsb3Q9XCJ0ZXh0YXJlYVwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcInJlc2l6ZS1ub25lIGJvcmRlci1pbnB1dCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpib3JkZXItcmluZyBmb2N1cy12aXNpYmxlOnJpbmctcmluZy81MCBhcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS8yMCBkYXJrOmFyaWEtaW52YWxpZDpyaW5nLWRlc3RydWN0aXZlLzQwIGFyaWEtaW52YWxpZDpib3JkZXItZGVzdHJ1Y3RpdmUgZGFyazpiZy1pbnB1dC8zMCBmbGV4IGZpZWxkLXNpemluZy1jb250ZW50IG1pbi1oLTE2IHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBiZy1pbnB1dC1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LWJhc2UgdHJhbnNpdGlvbi1bY29sb3IsYm94LXNoYWRvd10gb3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy1bM3B4XSBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MCBtZDp0ZXh0LXNtXCIsXG4gICAgICAgIGNsYXNzTmFtZSxcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKTtcbn1cblxuZXhwb3J0IHsgVGV4dGFyZWEgfTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiVGV4dGFyZWEiLCJjbGFzc05hbWUiLCJwcm9wcyIsInRleHRhcmVhIiwiZGF0YS1zbG90Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/utils.ts":
/*!************************************!*\
  !*** ./app/components/ui/utils.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy91aS91dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFDSjtBQUVsQyxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQmxhY2tcXERlc2t0b3BcXHNtYXJ0LW9mZi1wbGFuXFxhcHBcXGNvbXBvbmVudHNcXHVpXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiO1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiO1xuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/utils.ts\n");

/***/ }),

/***/ "(ssr)/./app/join/franchise/page.tsx":
/*!*************************************!*\
  !*** ./app/join/franchise/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BecomeFranchisePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/Navbar */ \"(ssr)/./app/components/Navbar.tsx\");\n/* harmony import */ var _components_BecomeFranchiseePage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/BecomeFranchiseePage */ \"(ssr)/./app/components/BecomeFranchiseePage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction BecomeFranchisePage() {\n    const handleLogoClick = ()=>{\n        // Navigate to home page\n        window.location.href = '/';\n    };\n    const handleBackToHome = ()=>{\n        window.location.href = '/';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-ivory\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__.Navbar, {\n                onLogoClick: handleLogoClick,\n                currentPage: \"become-franchisee\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BecomeFranchiseePage__WEBPACK_IMPORTED_MODULE_2__.BecomeFranchiseePage, {\n                onBack: handleBackToHome\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-soft-brown text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"section-padding\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"mb-6 text-white\",\n                                                children: \"Smart Off Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-tan text-sm mb-6 leading-relaxed\",\n                                                children: \"Your trusted partner for Dubai property investments. Connecting international investors with premium off-plan opportunities.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mb-6 text-white\",\n                                                children: \"Quick Links\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Home\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                            lineNumber: 43,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                        lineNumber: 42,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/properties\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Projects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                            lineNumber: 48,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                        lineNumber: 47,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/developers\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Developers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                            lineNumber: 53,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                        lineNumber: 52,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/about\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"About Us\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                            lineNumber: 58,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                        lineNumber: 57,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/contact\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Contact\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                        lineNumber: 62,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mb-6 text-white\",\n                                                children: \"Services\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/services/company-formation\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Company Formation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/services/mortgages\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Mortgages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                            lineNumber: 80,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/services/golden-visa\",\n                                                            className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                            children: \"Golden Visa\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"mb-6 text-white\",\n                                                children: \"Get In Touch\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-tan text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: \"\\uD83D\\uDCDE +971 4 123 4567\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                                lineNumber: 97,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: \"\\uD83D\\uDCE7 <EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                                lineNumber: 98,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: \"\\uD83D\\uDCCD Business Bay, Dubai, UAE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                                lineNumber: 99,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-white mb-3\",\n                                                                children: \"Working Hours\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                                lineNumber: 102,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-tan text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: \"Mon - Fri: 9:00 AM - 7:00 PM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                                        lineNumber: 104,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: \"Sat: 10:00 AM - 4:00 PM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                                        lineNumber: 105,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                                lineNumber: 103,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-12 h-px bg-gradient-to-r from-transparent via-gold to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-tan text-sm\",\n                                        children: \"\\xa9 2024 Smart Off Plan. All rights reserved.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-8 mt-4 md:mt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/privacy\",\n                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/terms\",\n                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/cookies\",\n                                                className: \"text-tan hover:text-gold transition-colors text-sm\",\n                                                children: \"Cookie Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\smart-off-plan\\\\app\\\\join\\\\franchise\\\\page.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/join/franchise/page.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cjoin%5C%5Cfranchise%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cjoin%5C%5Cfranchise%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/join/franchise/page.tsx */ \"(ssr)/./app/join/franchise/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0JsYWNrJTVDJTVDRGVza3RvcCU1QyU1Q3NtYXJ0LW9mZi1wbGFuJTVDJTVDYXBwJTVDJTVDam9pbiU1QyU1Q2ZyYW5jaGlzZSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBOEciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEJsYWNrXFxcXERlc2t0b3BcXFxcc21hcnQtb2ZmLXBsYW5cXFxcYXBwXFxcXGpvaW5cXFxcZnJhbmNoaXNlXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Capp%5C%5Cjoin%5C%5Cfranchise%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBlack%5C%5CDesktop%5C%5Csmart-off-plan%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fjoin%2Ffranchise%2Fpage&page=%2Fjoin%2Ffranchise%2Fpage&appPaths=%2Fjoin%2Ffranchise%2Fpage&pagePath=private-next-app-dir%2Fjoin%2Ffranchise%2Fpage.tsx&appDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBlack%5CDesktop%5Csmart-off-plan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();